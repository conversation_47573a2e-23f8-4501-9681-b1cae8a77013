package com.jinghang.cash.api.dto;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.api.enums.ActiveInactive;
import com.jinghang.cash.api.enums.FileType;
import lombok.Data;

import java.io.Serializable;

/**
 * 协议模板配置DTO
 *
 * @Author: Lior
 * @CreateTime: 2025/8/25 09:35
 */
@Data
public class ProjectAgreementDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private String id;

    /**
     * 项目唯一编码
     */
    private String projectCode;

    /**
     * 合同模板唯一编码
     */
    private String templateCode;

    /**
     * 合同模板类型
     */
    private FileType contractTemplateType;

    /**
     * 合同模板类型描述
     */
    private String contractTemplateDesc;

    /**
     * 资产方合同签署阶段
     */
    private String flowLoanStage;

    /**
     * 资金方合同签署阶段
     */
    private String capitalLoanStage;

    /**
     * 模板归属方
     */
    private String templateOwner;

    /**
     * 模板归属方名称
     */
    private String templateOwnerName;

    /**
     * 资金方合同名称
     */
    private String capitalContractName;

    /**
     * 资产方合同名称
     */
    private String flowContractName;

    /**
     * 是否融担签章
     */
    private ActiveInactive isRdSignature;

    /**
     * 签章类型
     */
    private String sealType;

    /**
     * 签章系统唯一编码
     */
    private String templateNo;

    /**
     * 是否回传资金方
     */
    private ActiveInactive isReturnToCapital;

    /**
     * 是否回传流量方
     */
    private ActiveInactive isReturnToFlow;

    /**
     * 模板备注
     */
    private String remark;

    /**
     * 启用状态
     */
    private AbleStatus enabled;

    /**
     * 文件路径
     */
    private String filePath;
}

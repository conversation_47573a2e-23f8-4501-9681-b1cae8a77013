package com.jinghang.cash.api.enums;

/**
 * 签章渠道
 */
public enum SignChannel {

    CFCA("CFCA", "CFCA"),
    EQB("EQB", "E签宝"),
    FDD("FDD", "法大大"),
    SSQ("SSQ", "上上签"),
    QYS("QYS", "契约锁");

    private final String code;

    private final String desc;

    SignChannel(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }


    public String getDesc() {
        return desc;
    }
}

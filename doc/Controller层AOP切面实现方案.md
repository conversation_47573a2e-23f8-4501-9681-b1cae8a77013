# Controller层AOP切面实现方案

## 🎯 方案确定

基于项目分析，确定使用**Controller层AOP切面**来拦截授信相关接口，记录请求响应数据。

## 📋 拦截目标分析

### 需要拦截的授信Controller（仅授信申请接口）

| Controller | 包路径 | 授信申请方法 |
|------------|---------|-------------|
| **LvxinCreditController** | `com.maguo.loan.cash.flow.entrance.lvxin.controller` | `creditApply` |
| **PpdCreditController** | `com.maguo.loan.cash.flow.entrance.ppd.controller` | `creditApply` |
| **FenQiLeCreditController** | `com.maguo.loan.cash.flow.entrance.fql.controller` | `creditApply` |

### 具体拦截的授信申请接口

| 接口路径 | 方法 | 功能描述 |
|----------|------|----------|
| `/lvxin/api/partner/v1/apply` | POST | 绿信授信申请 |
| `/lvxin/api/partner/v2/apply` | POST | 绿信授信申请(v2) |
| `/ppd/api/creditApply` | POST | 拍拍授信申请 |
| `/fenQiLe/api/creditApply` | POST | 分期乐授信申请 |

## 🔧 切点表达式设计

### 精确拦截授信申请方法
```java
@Pointcut("execution(* com.maguo.loan.cash.flow.entrance.lvxin.controller.LvxinCreditController.creditApply(..))" +
          " || execution(* com.maguo.loan.cash.flow.entrance.ppd.controller.PpdCreditController.creditApply(..))" +
          " || execution(* com.maguo.loan.cash.flow.entrance.fql.controller.FenQiLeCreditController.creditApply(..))")
public void creditApplyPointcut() {}
```

### 方法详细信息

| Controller | 方法签名 | 请求参数 | 响应类型 |
|------------|----------|----------|----------|
| **LvxinCreditController** | `creditApply(ApprovalRequest)` | `ApprovalRequest` | `LvxinResponse` |
| **PpdCreditController** | `creditApply(CreditApplyRequest, BindingResult)` | `CreditApplyRequest` | `ApiResult` |
| **FenQiLeCreditController** | `creditApply(FenQiLeCreditApplyRequest, BindingResult)` | `FenQiLeCreditApplyRequest` | `FenQiLeCreditApplyResponse` |

## 📊 数据获取策略

### 1. 请求数据获取
```java
// 从Controller方法参数获取
Object[] args = joinPoint.getArgs();
String requestData = JsonUtil.toJsonString(args);

// 获取HTTP请求信息
HttpServletRequest request = ((ServletRequestAttributes) 
    RequestContextHolder.currentRequestAttributes()).getRequest();
String requestIp = getClientIpAddress(request);
String userAgent = request.getHeader("User-Agent");
```

### 2. 响应数据获取
```java
// 从Controller方法返回值获取
Object result = joinPoint.proceed();
String responseData = JsonUtil.toJsonString(result);
```

### 3. 业务信息提取
```java
// 从请求参数中提取业务信息
String businessId = extractBusinessId(args);
String certNo = extractCertNo(args);
String mobile = extractMobile(args);
```

## 🏗️ 核心实现架构

```
HTTP请求 → Controller方法 → AOP拦截器 → 记录日志 → 返回响应
                ↓
            1. 记录请求参数
            2. 执行目标方法  
            3. 记录响应数据
            4. 异步保存日志
```

## ⚡ 性能优化策略

### 1. 异步处理
- 使用`@Async`异步保存日志
- 配置独立线程池处理日志任务
- 避免影响主业务性能

### 2. 数据大小限制
- 限制请求参数最大长度（如10KB）
- 限制响应数据最大长度（如10KB）
- 超长数据截断处理

### 3. 异常处理
- 日志记录失败不影响主业务
- 提供降级策略
- 完善的异常捕获和处理

## 🔍 实现细节

### 1. 切面执行顺序
```java
@Order(1) // 确保在其他切面之前执行
@Aspect
@Component
public class CreditApiLogAspect {
    // 实现逻辑
}
```

### 2. 条件化启用
```java
@ConditionalOnProperty(
    prefix = "credit.api.log", 
    name = "enabled", 
    havingValue = "true", 
    matchIfMissing = true
)
```

### 3. 配置化管理
```yaml
credit:
  api:
    log:
      enabled: true
      async: true
      max-request-size: 10240
      max-response-size: 10240
```

## 🎯 实现优势

1. **精确拦截**：只拦截授信相关的Controller方法
2. **完整信息**：获取HTTP请求的完整信息
3. **无侵入性**：不需要修改现有业务代码
4. **高性能**：异步处理，不影响主业务
5. **易维护**：统一的日志管理和配置

## 📝 下一步实现计划

1. ✅ 确定切面拦截方案
2. ⏳ 创建CreditApiLog实体类和数据库表
3. ⏳ 实现AOP切面拦截器
4. ⏳ 创建日志服务类
5. ⏳ 配置和测试验证

这个方案可以精确地拦截所有授信相关的Controller接口，记录完整的请求响应数据，为业务监控和问题排查提供强有力的支撑。

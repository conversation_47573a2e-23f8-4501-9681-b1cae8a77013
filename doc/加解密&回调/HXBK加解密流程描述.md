5.2	加解密流程描述
本平台所有接口请求响应均需进行数字签名校验，报文的加密是可选项按需使用。
在加解密过程中会使用到128位AES/ECB/PKCS5Padding对称加密算法和2048位RSA非对称加密算法，SHA256WithRSA实现加签验签。

实现概述
双方各自生成自己的RSA 2048位密钥对，使用对方公钥加密，己方私钥解密，使用己方私钥加签，对方公钥验签，详细如下：
1、合作方生成秘钥对：public1,private1。
2、蚂蚁侧生成密钥对：public2,private2，sccretKey。
3、双方交换public1、public2以及secretKey。
4、合作方使用public2加密secretKey，蚂蚁侧使用private2解密secretKey，蚂蚁侧使用明文secretKey解密业务报文。
5、合作方使用private1加签请求报文，蚂蚁侧使用public1验签报文。
6、蚂蚁侧使用public1加密secretKey，合作方使用private1解密secretKey，合作方使用明文secretKey解密业务报文。
7、蚂蚁侧使用private2加签响应报文，合作方使用public2验签响应报文。

加密加签
1、请求或响应之前使用secretKey调用AES加密业务报文，赋值给data字段，加密业务报文操作完成。
2、使用RSA加密secretKey，secretKey加密操作完成。
3、将请求或响应报文的所有字段除sign外按照字母升序排序后按照key1=value1&key2=value2的方式拼接，获取待签名字符串。
4、使用SHA256WithRSA算法对上述字符串做加签操作，并赋值给sign加签操作完成。

解密验签
1、将请求或者响应报文的所有字段除sign外按照字母升序排序后按照key1=value1&key2=value2的方式拼接，示例参考加密加签。
2、使用SHA256WithRSA算法对上述字符串做验签操作，验签不通过返回异常码。
3、使用RSA解密secretKey，使用明文secretKey解密业务参数获得业务参数明文。

5.2.1	加解密工具类
RSA2048KeyPairGenerator
AESUtils
RSAUtils
ParamUtils
PartnerDemo
RSA2048KeyPairGenerator（RSA2048位密钥对生成器）
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
/**
 * RSA 2048位密钥对生成器
 */
public class RSA2048KeyPairGenerator {
public static void main(String[] args) {
try {
// 创建一个密钥对生成器实例，指定加密算法为RSA
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(2048); // 指定密钥长度为2048位
            // 生成密钥对
            KeyPair keyPair = keyPairGenerator.generateKeyPair();
            // 获取公钥和私钥
            String publicKey = Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());
            String privateKey = Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
            // 打印公钥和私钥的Base64编码字符串
            System.out.println("公钥: " + publicKey);
            System.out.println("私钥: " + privateKey);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
    }
}

AESUtils（业务数据加解密工具类）
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * AES加解密业务数据
 */
public class AESUtils {

    private static final String KEY_ALGORITHM            = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";

    /**
     * 使用AES算法加密数据
     *
     * @param content 待加密内容
     * @param key     加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static String encryptDataWithAES(String content, String key) {
        try {
            if (content == null) {
                return null;
            }
            // 创建密码器
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            // 初始化为加密模式的密码器
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(key));
            // 加密
            byte[] result = cipher.doFinal(byteContent);
            // 通过Base64转码返回
            return Base64.encodeBase64String(result);
        } catch (Exception e) {
            System.out.println("AESUtil.encrypt-error:" + e);
        }
        return null;
    }

    /**
     * 使用AES算法解密数据
     *
     * @param content
     * @param key
     * @return
     * @throws Exception
     */
    public static String decryptDataWithAES(String content, String key) {
        try {
            if (content == null) {
                return null;
            }
            // 实例化
            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            // 使用密钥初始化，设置为解密模式
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(key));
            // 执行操作
            byte[] result = cipher.doFinal(Base64.decodeBase64(content));
            return new String(result, StandardCharsets.UTF_8);
        } catch (Exception e) {
            System.out.println("AESUtil.decrypt-error:" + e);
        }
        return null;
    }

    /**
     * 生成加密秘钥
     */
    private static SecretKeySpec getSecretKey(final String key) {
        // 返回生成指定算法密钥生成器的 KeyGenerator 对象
        KeyGenerator kg;
        try {
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(key.getBytes());
            kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            // AES 要求密钥长度为 128
            kg.init(128, random);
            // 生成一个密钥
            SecretKey secretKey = kg.generateKey();
            // 转换为AES专用密钥
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            System.out.println("AESUtil.getSecretKey-error:" + e);
        }
        return null;
    }

}


RSAUtils（RSA加解密SecretKey及SHA256WithRSA加签验签工具类）
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA加解密SecretKey密钥
 * SHA256WithRSA加签、验签
 */
public class RSAUtils {

    private static final String RSA_ALGORITHM       = "RSA";
    private static final String SIGNATURE_ALGORITHM = "SHA256WithRSA";

    /**
     * 使用RSA算法加密SecretKey密钥
     *
     * @param secretKey
     * @param publicKeyStr
     * @return
     * @throws Exception
     */
    public static String encryptSecretKeyWithRSA(String secretKey, String publicKeyStr) {
        try {
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Cipher rsaCipher = Cipher.getInstance(RSA_ALGORITHM);
            rsaCipher.init(Cipher.ENCRYPT_MODE, publicKey);
            byte[] encryptedKeyBytes = rsaCipher.doFinal(secretKey.getBytes());
            return Base64.encodeBase64String(encryptedKeyBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 使用RSA算法解密SecretKey密钥
     *
     * @param encryptedSecretKey
     * @param privateKeyStr
     * @return
     * @throws Exception
     */
    public static String decryptSecretKeyWithRSA(String encryptedSecretKey, String privateKeyStr) {
        try {
            PrivateKey privateKey = loadPrivateKey(privateKeyStr);
            Cipher rsaCipher = Cipher.getInstance(RSA_ALGORITHM);
            rsaCipher.init(Cipher.DECRYPT_MODE, privateKey);
            byte[] encryptedKeyBytes = Base64.decodeBase64(encryptedSecretKey);
            byte[] decryptedKeyBytes = rsaCipher.doFinal(encryptedKeyBytes);
            return new String(decryptedKeyBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 签名
     *
     * @param dataToSign
     * @param privateKeyStr
     * @return
     * @throws Exception
     */
    public static String signData(String dataToSign, String privateKeyStr) {
        try {
            PrivateKey privateKey = loadPrivateKey(privateKeyStr);
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initSign(privateKey);
            signature.update(dataToSign.getBytes());
            byte[] signatureBytes = signature.sign();
            return Base64.encodeBase64String(signatureBytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 验证签名
     *
     * @param signedData
     * @param dataToVerify
     * @param publicKeyStr
     * @return
     * @throws Exception
     */
    public static boolean verifyData(String signedData, String dataToVerify, String publicKeyStr) {
        try {
            PublicKey publicKey = loadPublicKey(publicKeyStr);
            Signature signature = Signature.getInstance(SIGNATURE_ALGORITHM);
            signature.initVerify(publicKey);
            signature.update(dataToVerify.getBytes());
            return signature.verify(Base64.decodeBase64(signedData));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    private static PublicKey loadPublicKey(String key) throws Exception {
        byte[] keyBytes = java.util.Base64.getDecoder().decode(key);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePublic(keySpec);
    }

    private static PrivateKey loadPrivateKey(String key) throws Exception {
        byte[] keyBytes = java.util.Base64.getDecoder().decode(key);
        PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance(RSA_ALGORITHM);
        return keyFactory.generatePrivate(keySpec);
    }

}

ParamUtils（参数解析工具类）
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;

import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 参数解析工具类
 */
public class ParamUtils {

    /**
     * 解析请求参数
     *
     * @param bodyJson
     * @return paramsToVerify 解析后的请求参数
     */
    public static TreeMap<String, String> parseParamFromRequest(JSONObject bodyJson) {
        TreeMap<String, String> paramsToVerify = new TreeMap<>();
        paramsToVerify.put("appid", bodyJson.getString("appid"));
        paramsToVerify.put("method", bodyJson.getString("method"));
        paramsToVerify.put("version", bodyJson.getString("version"));
        paramsToVerify.put("timestamp", bodyJson.getString("timestamp"));
        paramsToVerify.put("signType", bodyJson.getString("signType"));
        paramsToVerify.put("encrypt", bodyJson.getString("encrypt"));
        String encryptedSecretKey = bodyJson.getString("secretKey");
        System.out.println("接收到的加密SecretKey: " + encryptedSecretKey);
        paramsToVerify.put("secretKey", encryptedSecretKey);
        String encryptedBizData = bodyJson.getString("data");
        System.out.println("接收到的业务加密数据: " + encryptedBizData);
        paramsToVerify.put("data", encryptedBizData);
        paramsToVerify.put("requestId", bodyJson.getString("requestId"));
        return paramsToVerify;
    }

    /**
     * 构建返回的业务响应
     *
     * @param response      加密后的业务响应数据
     * @param bizResDataStr 业务数据
     * @param publicKey     对方公钥
     * @param privateKey    己方私钥
     * @return
     */
    public static String buildResponse(TreeMap<String, String> response, String bizResDataStr, String publicKey, String privateKey) {
        // 生成secretKey，可以是uuid（建议），也可以是实际的固定值
        String secretKey = UUID.randomUUID().toString();
        // 使用对方公钥加密secretKey
        String encryptedSecretKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, publicKey);
        System.out.println("加密后的SecretKey: " + encryptedSecretKey);

        // 加密业务参数
        String encryptedBizResData = AESUtils.encryptDataWithAES(bizResDataStr, secretKey);
        System.out.println("加密后的响应结果: " + encryptedBizResData);

        response.put("data", encryptedBizResData);
        response.put("encrypt", "1");
        response.put("secretKey", encryptedSecretKey);
        response.put("responseId", UUID.randomUUID().toString());

        // 构建待加签的字符串（code, data, encrypt, msg, responseId, secretKey，仅这6个字段参与签名串，按首字母升序排列)
        String toSignStr = response.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
        System.out.println("构建待加签的字符串: " + toSignStr);

        // 使用私钥加签
        String responseSignature = RSAUtils.signData(toSignStr, privateKey);
        System.out.println("响应签名: " + responseSignature);

        // 将签名返回
        response.put("sign", responseSignature);

        return JSON.toJSONString(response);
    }

    /**
     * 构建请求参数
     *
     * @param method     请求的接口
     * @param bizDataStr 业务数据
     * @param publicKey  对方公钥
     * @param privateKey 己方私钥
     * @return
     */
    public static String buildRequest(String method, String bizDataStr, String publicKey, String privateKey) {
        // 生成secretKey，可以是uuid（建议），也可以是实际的固定值
        String secretKey = UUID.randomUUID().toString();
        // 第1步：使用对方公钥加密secretKey
        String encryptedSecretKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, publicKey);
        System.out.println("加密后的SecretKey: " + encryptedSecretKey);

        // 第2步：加密业务数据
        String encryptedData = AESUtils.encryptDataWithAES(bizDataStr, secretKey);
        System.out.println("加密后的业务数据: " + encryptedData);

        // 第3步：构建请求参数
        TreeMap<String, String> params = new TreeMap<>();
        // 标识，固定值 dubhe
        params.put("appid", "dubhe");
        // 业务接口标识， 标识唯一的业务接口，以实际为准
        params.put("method", method);
        // 版本号，固定值 1.0
        params.put("version", "1.0");
        // 时间戳
        params.put("timestamp", String.valueOf(System.currentTimeMillis()));
        // 签名方式，固定值 SHA256WithRSA
        params.put("signType", "SHA256WithRSA");
        // 加密标识，默认为1
        params.put("encrypt", "1");
        // 加密秘钥
        params.put("secretKey", encryptedSecretKey);
        // 业务参数
        params.put("data", encryptedData);
        // 请求ID，标识一次请求，保证唯一
        params.put("requestId", UUID.randomUUID().toString());

        // 第4步：构建待签名字符串
        String toSignStr = params.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
        System.out.println("待签名字符串: " + toSignStr);

        // 第5步：使用私钥加签
        String signature = RSAUtils.signData(toSignStr, privateKey);
        System.out.println("签名Signature: " + signature);

        // 数字签名
        params.put("sign", signature);

        return JSON.toJSONString(params);
    }

    /**
     * 验签并解密收到的响应
     *
     * @param responseStr 接收到的响应
     * @param publicKey   对方公钥
     * @param privateKey  己方私钥
     */
    public static JSONObject verifySignAndDecryptResponse(String responseStr, String publicKey, String privateKey) {
        JSONObject resultJson = new JSONObject();

        JSONObject response = JSON.parseObject(responseStr);
        String code = response.getString("code");
        resultJson.put("code", code);
        String msg = response.getString("msg");
        resultJson.put("msg", msg);

        if (!"000000".equals(code)) {
            System.out.println("响应异常: " + responseStr);
            return resultJson;
        }

        String respBizData = response.getString("data");
        System.out.println("接收到的业务加密数据: " + respBizData);
        String encrypt = response.getString("encrypt");
        String encryptedSecretKey = response.getString("secretKey");
        System.out.println("接收到的加密后SecretKey: " + encryptedSecretKey);
        String responseId = response.getString("responseId");

        // 构建响应的待签名字符串
        TreeMap<String, String> paramsToVerify = new TreeMap<>();
        paramsToVerify.put("code", code);
        paramsToVerify.put("msg", msg);
        paramsToVerify.put("data", respBizData);
        paramsToVerify.put("encrypt", encrypt);
        paramsToVerify.put("secretKey", encryptedSecretKey);
        paramsToVerify.put("responseId", responseId);

        // 构建待验签的字符串（code, data, encrypt, msg, responseId, secretKey，仅这6个字段参与签名串，按首字母升序排列)
        String toVerifyStr = paramsToVerify.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
        System.out.println("响应数据的待签名字符串: " + toVerifyStr);

        // 响应数据验签
        String signature = response.getString("sign");
        boolean verified = RSAUtils.verifyData(signature, toVerifyStr, publicKey);
        System.out.println("响应数据验签 : " + verified);
        if (verified) {
            // 解密secretKey
            String secretKey = RSAUtils.decryptSecretKeyWithRSA(encryptedSecretKey, privateKey);
            // 解密响应数据
            String decryptedResponseData = AESUtils.decryptDataWithAES(respBizData, secretKey);
            System.out.println("解密后的响应数据: " + decryptedResponseData);
            if (StringUtils.isBlank(decryptedResponseData)) {
                resultJson.put("code", "200000");
                resultJson.put("msg", "响应数据解密失败");
                return resultJson;
            } else {
                JSONObject bizResultJson = JSON.parseObject(decryptedResponseData);
                resultJson.put("data", bizResultJson);
                return resultJson;
            }
        } else {
            System.out.println("响应验签失败");
            resultJson.put("code", "100000");
            resultJson.put("msg", "响应验签失败");
            return resultJson;
        }
    }
}

PartnerDemo
1）acceptAntRequest：接受蚂蚁侧请求（以“授信准入”接口的响应为范例）
2）callbackDemo：回调请求蚂蚁侧服务（以“授信结果回调”接口为范例）
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 机构中心接口交互Demo
 */
public class PartnerDemo {

    // 合作方私钥，以实际为准
    private static final String partnerPrivateKey = "使用 RSA2048KeyPairGenerator 生成的私钥";

    // 蚂蚁公钥，以实际为准
    private static final String antPublicKey = "参见【环境信息】sheet";

    /**
     * 接受请求，并返回响应结果
     */
    public static String acceptAntRequest(String antRequest) {
        JSONObject bodyJson = JSON.parseObject(antRequest);
        // 第1步：解析入参
        TreeMap<String, String> paramsToVerifyMap = ParamUtils.parseParamFromRequest(bodyJson);

        String toVerifyStr = paramsToVerifyMap.entrySet().stream().map(e -> e.getKey() + "=" + e.getValue()).collect(Collectors.joining("&"));
        System.out.println("待验签字符串: " + toVerifyStr);

        // 第2步：验签
        String signature = bodyJson.getString("sign");
        boolean verified = RSAUtils.verifyData(signature, toVerifyStr, antPublicKey);
        System.out.println("验签: " + verified);

        if (verified) {
            // 第3步：使用己方私钥解密SecretKey
            String secretKey = RSAUtils.decryptSecretKeyWithRSA(bodyJson.getString("secretKey"), partnerPrivateKey);
            System.out.println("secretKey的明文: " + secretKey);

            // 第4步：解密业务数据
            String bizData = AESUtils.decryptDataWithAES(bodyJson.getString("data"), secretKey);
            System.out.println("业务参数bizData: " + bizData);

            // 第5步：处理自己业务逻辑
            doOwnBusiness(bizData);

            // 第6步：构建响应
            String responseStr = buildResponse();
            System.out.println("响应结果Response: " + responseStr);

            return responseStr;
        } else {
            System.out.println("验签失败");
            TreeMap<String, String> response = new TreeMap<>();
            response.put("code", "100000");
            response.put("msg", "验签失败");
            response.put("requestId", UUID.randomUUID().toString());
            return JSON.toJSONString(response);
        }
    }

    /**
     * 合作方内部业务逻辑
     *
     * @param antBizData
     */
    private static void doOwnBusiness(String antBizData) {
        System.out.println("\n****** 合作方内部业务逻辑 ******\n");
    }

    /**
     * 构建返回蚂蚁侧的响应
     */
    private static String buildResponse() {
        // 第1步：响应的业务数据，以实际为准
        JSONObject bizResData = buildBizDataResponse();
        // 响应码，以实际为准
        String code = "000000";
        // 描述，对应的描述信息，以实际为准
        String msg = "请求成功";

        // 第2步：构建响应
        TreeMap<String, String> response = new TreeMap<>();
        response.put("code", code);
        response.put("msg", msg);
        String respJson = ParamUtils.buildResponse(response, bizResData.toJSONString(), antPublicKey, partnerPrivateKey);

        return respJson;
    }

    /**
     * 响应的业务数据，以实际为准
     *
     * @return
     */
    private static JSONObject buildBizDataResponse() {
        // 构建业务参数：以[授信准入]为范例
        JSONObject bizResData = new JSONObject();
        bizResData.put("enableApply", "Y");
        return bizResData;
    }

    /**
     * 合作方回调请求蚂蚁侧服务（授信、放款、还款结果回调等）
     *
     * @param partnerPrivateKey 合作方私钥
     * @param antPublicKey      蚂蚁公钥
     */
    public static void callbackDemo(String partnerPrivateKey, String antPublicKey) {

        // 第1步：构建参数
        // [授信结果回调]业务接口，标识唯一的业务接口，以实际为准
        String method = "dubhe.credit.apply.result.notify";
        // [授信结果回调]业务参数，以实际为准
        JSONObject bizData = buildCallbackBizData();

        String requestStr = ParamUtils.buildRequest(method, bizData.toJSONString(), antPublicKey, partnerPrivateKey);
        System.out.println("加签后的请求参数: " + requestStr);

        // 第2步：模拟回调请求蚂蚁侧服务，并获得响应
        // String antResponseStr = "请求蚂蚁侧服务后的到的响应报文";
        String antResponseStr = "{\"msg\":\"请求成功\",\"code\":\"000000\",\"data\":\"...\",\"secretKey\":\"...\"}";

        // 第3步：验签并解密蚂蚁侧的响应
        JSONObject resultJson = ParamUtils.verifySignAndDecryptResponse(antResponseStr, antPublicKey, partnerPrivateKey);
        System.out.println("解密后的响应报文: " + resultJson.toJSONString());
        // do other something ...
    }

    /**
     * 业务参数
     *
     * @return
     */
    private static JSONObject buildCallbackBizData() {
        // 构建业务参数：以[授信结果回调]为范例
        JSONObject bizData = new JSONObject();
        bizData.put("applySerialNo", "20240222");
        bizData.put("status", "F");
        bizData.put("channelCode", "dingtalk");
        bizData.put("platformNo", "D20240222");
        bizData.put("customNo", "PCM20240118");
        bizData.put("rejectCode", "REJ_CODE_1003");
        bizData.put("msg", "综合评分不足");
        return bizData;
    }

    public static void main(String[] args) {
        // 回调请求蚂蚁侧Demo
        callbackDemo(partnerPrivateKey, antPublicKey);
    }

}

5.3	环境信息
5.3.1	蚂蚁公钥：
开发、测试：
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5uN5D9Uow0RhBpXTY0ee3iYQadbuoQTKCA1LTETPVww/4pOHB6yaecFYwiyzDzePpeFQkWbLWMow/RR/Jm7XEAHxDitrXQqgiWaRzgpcCP0ct7SjpOfYHfdXpl7kjhKQmlR5YNdnsM/4iQm7y6nAITsqqq07hWlBRX+iUPZ/uVxp8gYgEoqTSKMROiRStnswUGIf3hzwmUsee/vfxhY8H2s+5gtzHX3kJ1dVVAETTOaLVFY6PodAscTHOpO8JPM1aVlHee7HafMJNeycrZ4QkK6lH905peBwM5XYumTO0ftFFM+9IOU9nyJPneiTpnO0yDE9mcM5WKOe9qSnkeBBjQIDAQAB
预发、生产：
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAhLBnSSMWF431iV5u634TtEp2bQKkh3Yt/q8KvcJX99mGE9VjTpjMpcss+sljCEvi67mwEhKTm217hcUVxc0lBoUALf+y9+LRfvqATjbCVtuYCLir5wy1wxPEHPtrewZwGCegU2u7/z9nkjyn0puE9xZH4rOWem4lYKR+w6Y7LhSrRQ6gYG96yggliBd3mXjUNDDQK4+fzYuMuMR9P8Z990JpBwLLtK21IepOSiwFTrnPofBQc0BLIeYrE09wqNxCOtIv9aFoeqpAt3VL3Rco6LmAW5Tf0NCxPdKmv1YILLNdgceK423kkbpUgGl96u4C+DjeZUoD5qdJF5snvwN0DQIDAQAB

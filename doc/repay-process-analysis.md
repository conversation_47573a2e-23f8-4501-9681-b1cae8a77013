# 还款流程分析文档

## 概述
本文档详细分析了现金贷系统的还款流程，包括线上还款和线下还款的完整过程，以及各个表的状态变化。

## 核心表结构

### 1. CustomRepayRecord (对客还款记录表)
- **作用**: 记录用户发起的还款请求和处理结果
- **关键字段**:
  - `repayState`: 还款状态 (INIT → PROCESSING → SUCCEED/FAILED)
  - `repayMode`: 还款模式 (ONLINE/OFFLINE)
  - `repayPurpose`: 还款目的 (CURRENT/CLEAR)
  - `totalAmt`: 还款总金额
  - `reduceAmount`: 减免金额

### 2. BankRepayRecord (对资还款记录表)
- **作用**: 记录向资方发起的还款请求和结果
- **关键字段**:
  - `state`: 处理状态 (INIT → PROCESSING → SUCCEED/FAILED)
  - `sourceRecordId`: 关联的对客还款记录ID
  - `bankRepayNo`: 资方返回的还款流水号

### 3. RepayPlan (还款计划表)
- **作用**: 记录每期的还款计划和实际还款情况
- **关键字段**:
  - `custRepayState`: 客户还款状态 (NORMAL → REPAID)
  - `actRepayTime`: 实际还款时间
  - `actAmount`: 实际还款金额

### 4. Loan (借据表)
- **作用**: 记录放款信息和状态
- **关键字段**:
  - `loanState`: 放款状态
  - `flowChannel`: 流量渠道
  - `bankChannel`: 资金渠道

## 还款流程详解

### 线上还款流程

#### 1. 还款申请阶段
```
用户发起还款请求 → RepayService.online()
```

**状态变化**:
- 创建 `CustomRepayRecord` 记录，状态为 `INIT`
- 进行还款试算和校验

**主要步骤**:

##### 1.1 系统维护时间校验
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
// 系统维护校验
if (StringUtil.isNotBlank(repayMaintainStart) && StringUtil.isNotBlank(repayMaintainEnd)) {
    LocalDateTime start = LocalDateTime.parse(repayMaintainStart, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    LocalDateTime end = LocalDateTime.parse(repayMaintainEnd, DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    if (LocalDateTime.now().isAfter(start) && LocalDateTime.now().isBefore(end)) {
        throw new BizException(repayMaintainMsg, ResultCode.SYS_MAINTAIN);
    }
}
````
</augment_code_snippet>

##### 1.2 分布式锁防重复提交
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
// 所有还款相关操作互斥
String lockKey = "cash_business_repay_" + request.getLoanId();
Locker lock = lockService.getLock(lockKey);

try {
    boolean locked = lock.tryLock(Duration.ZERO, Duration.ofSeconds(REPAY_LOCK_RELEASE_SECOND));
    if (!locked) {
        throw new BizException(ResultCode.NO_SUBMIT_REPEAT);
    }
    // ... 还款处理逻辑
} finally {
    lock.unlock();
}
````
</augment_code_snippet>

##### 1.3 校验还款记录
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private void checkRepayRecord(String loanId, Integer period) {
    // 对客的还款记录
    List<CustomRepayRecord> byLoanIdAndPeriod = recordRepository.findAllByLoanIdAndPeriod(loanId, period);
    if (!CollectionUtils.isEmpty(byLoanIdAndPeriod)) {
        if (byLoanIdAndPeriod.stream().anyMatch(customRepayRecord ->
            StringUtils.equalsAny(customRepayRecord.getRepayState().name(),
                ProcessState.SUCCEED.name(), ProcessState.INIT.name(), ProcessState.PROCESSING.name()))) {
            logger.error("当前订单正在处理中 loanId={} period={}", loanId, period);
            throw new BizException(ResultCode.REPAY_CHECK_ERROR);
        }
    }
}
````
</augment_code_snippet>

##### 1.4 初始化对客还款记录
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private CustomRepayRecord initRepayRecord(OnlineRepayRequestDto request, Loan loan) {
    CustomRepayRecord customRepayRecord = new CustomRepayRecord();
    customRepayRecord.setLoanId(loan.getId());
    customRepayRecord.setPeriod(request.getPeriod());
    customRepayRecord.setRepayApplyDate(LocalDateTime.now());
    customRepayRecord.setRepayPurpose(request.getRepayPurpose());
    customRepayRecord.setRepayMode(RepayMode.ONLINE);
    customRepayRecord.setRepayState(ProcessState.INIT);
    customRepayRecord.setNeedTwiceState(WhetherState.N);
    customRepayRecord.setPaySide(PaySide.CAPITAL);

    CustomRepayRecord record = recordRepository.save(customRepayRecord);
    logger.info("对客还款记录id:{}", record.getId());
    return record;
}
````
</augment_code_snippet>

##### 1.5 还款试算
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
try {
    // 还款试算
    if (StringUtil.isBlank(request.getRepayDate())) {
        request.setRepayDate(lvxinSysTimeMockService.isMockTime(LocalDateTime.now())
            .format(DateTimeFormatter.ofPattern("yyyyMMdd")));
    }
    trialResultVo = trialService.repayTrial(loanId, repayPurpose, period, request.getRepayDate());
    customRepayRecord = updateNormalReapyRecord(customRepayRecord, trialResultVo);
} catch (Exception e) {
    logger.error("还款试算异常,loanId:{}", loanId, e);
    updateTrialFailReapyRecord(customRepayRecord, e);
    throw e;
}
````
</augment_code_snippet>

##### 1.6 减免金额校验 (仅LVXIN渠道)
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
//区分渠道，做减免
if (FlowChannel.LVXIN.equals(loan.getFlowChannel())) {
    //判断是否逾期还款并且判断是不是在是否可以使用宽限期
    //且宽限期无减免：
    reduction(customRepayRecord, loanId, period, request, trialResultVo, loan);
} else {
    chargeService.chargeMotherAndChild(customRepayRecord, loan);
    BankRepayRecord bankRepayRecord = this.initBankRepayRecord(request, trialResultVo, customRepayRecord.getId(), loan);
    mqService.submitRepay(bankRepayRecord.getId());
}
````
</augment_code_snippet>

#### 2. 扣款申请阶段
```
试算成功 → 创建BankRepayRecord → 发送MQ消息
```

**状态变化**:
- 创建 `BankRepayRecord` 记录，状态为 `INIT`
- 发送 `submitRepay(bankRepayRecordId)` 消息到MQ

##### 2.1 创建对资还款记录
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private BankRepayRecord initBankRepayRecord(OnlineRepayRequestDto request, TrialResultVo trialResultVo,
                                          String customRepayRecordId, Loan loan) {
    BankRepayRecord bankRepayRecord = new BankRepayRecord();
    bankRepayRecord.setSourceRecordId(customRepayRecordId);
    bankRepayRecord.setLoanId(loan.getId());
    bankRepayRecord.setPeriod(request.getPeriod());
    bankRepayRecord.setRepayPurpose(request.getRepayPurpose());
    bankRepayRecord.setRepayMode(RepayMode.ONLINE);
    bankRepayRecord.setRepayType(RepayType.REPAY);
    bankRepayRecord.setState(ProcessState.INIT);

    // 设置各项金额
    bankRepayRecord.setAmount(trialResultVo.getAmount());
    bankRepayRecord.setPrincipal(trialResultVo.getPrincipal());
    bankRepayRecord.setInterest(trialResultVo.getInterest());
    bankRepayRecord.setGuarantee(trialResultVo.getCapitalGuaranteeFee());
    bankRepayRecord.setPenalty(trialResultVo.getCapitalPenalty());

    return bankRepayRecordRepository.save(bankRepayRecord);
}
````
</augment_code_snippet>

##### 2.2 发送MQ消息
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/MqService.java" mode="EXCERPT">
````java
/**
 * 还款申请
 * @param repayId
 */
public void submitRepay(String repayId) {
    submit(repayId, null, RabbitConfig.Exchanges.REPAY, RabbitConfig.RoutingKeys.REPAY_APPLY, null);
}
````
</augment_code_snippet>

#### 3. 资方扣款阶段
```
MQ消息处理 → RepayService.repay() → 调用资方接口
```

**状态变化**:
- `CustomRepayRecord.repayState`: INIT → PROCESSING
- `BankRepayRecord.state`: INIT → PROCESSING (成功时)
- 设置 `bankRepayNo` (资方流水号)

##### 3.1 MQ消息监听处理
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/listener/RepayListener.java" mode="EXCERPT">
````java
@RabbitListener(queues = RabbitConfig.Queues.REPAY_APPLY)
public void listenRepayApply(Message message, Channel channel) {
    String repayId = new String(message.getBody(), StandardCharsets.UTF_8);
    try {
        logger.info("还款申请core:{}", repayId);
        repayService.repay(repayId);
    } catch (Exception e) {
        warningService.warn("还款申请core异常:" + repayId + "," + e.getMessage(),
            msg -> logger.error("还款申请core异常:{},", repayId, e));
    } finally {
        ackMsg(repayId, message, channel);
    }
}
````
</augment_code_snippet>

##### 3.2 获取银行卡信息并构建参数
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
public void repay(String repayId) {
    BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findById(repayId)
        .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
    CustomRepayRecord customRepayRecord = recordRepository.findById(bankRepayRecord.getSourceRecordId())
        .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
    Loan loan = loanRepository.findById(bankRepayRecord.getLoanId())
        .orElseThrow(() -> new BizException(ResultCode.BIZ_ERROR));

    // 用户阶段还款银行卡
    UserBankCard userBankCard = platformOnceBoundService.obtainRepayCard(loan);
    WithholdFlow withholdFlow = withholdFlowRepository.findByRepayRecordId(customRepayRecord.getId());

    RepayApplyDto repayApplyDto = new RepayApplyDto();
    this.buildRepayApply(repayApplyDto, bankRepayRecord, loan, userBankCard);

    // 更新对客还款记录为处理中
    customRepayRecord.setRepayState(ProcessState.PROCESSING);
    recordRepository.save(customRepayRecord);
}
````
</augment_code_snippet>

##### 3.3 调用资方接口
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
RestResult<RepayResultDto> restResult;
// 通知资方扣款
try {
    logger.info("还款申请,core param:{}", JsonUtil.toJsonString(repayApplyDto));
    restResult = finRepayService.repay(repayApplyDto);
    logger.info("还款申请,core返回:{}", JsonUtil.toJsonString(restResult));
} catch (Exception e) {
    // 异常后也要查询,确保不重复发起
    mqService.submitRepayQueryDelay(bankRepayRecord.getId());
    throw e;
}

if (restResult.isSuccess()) {
    // 请求成功
    bankRepayRecord.setState(ProcessState.PROCESSING);
    bankRepayRecord.setBankRepayNo(restResult.getData().getRepayId());
    bankRepayRecordRepository.save(bankRepayRecord);
}

mqService.submitRepayQueryDelay(bankRepayRecord.getId());
````
</augment_code_snippet>

#### 4. 结果查询阶段
```
延迟MQ消息 → RepayService.repayResult() → 查询资方结果
```

**成功时状态变化**:
- `BankRepayRecord.state`: PROCESSING → SUCCEED
- `RepayPlan.custRepayState`: NORMAL → REPAID
- `RepayPlan.actRepayTime`: 设置实际还款时间
- `RepayPlan.actAmount`: 设置实际还款金额

**失败时状态变化**:
- `BankRepayRecord.state`: PROCESSING → FAILED
- `CustomRepayRecord.repayState`: PROCESSING → FAILED
- 设置失败原因 `failReason`

##### 4.1 MQ延迟查询监听
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/listener/RepayResultListener.java" mode="EXCERPT">
````java
@RabbitListener(queues = RabbitConfig.Queues.REPAY_QUERY)
public void listenRepayResult(Message message, Channel channel) {
    String repayId = new String(message.getBody(), StandardCharsets.UTF_8);
    try {
        logger.info("监听core还款结果:{}", repayId);
        repayService.repayResult(repayId);
    } catch (Exception e) {
        processException(repayId, message, e, "查询core还款结果异常", getMqService()::submitRepayQueryDelay);
    } finally {
        ackMsg(repayId, message, channel);
    }
}
````
</augment_code_snippet>

##### 4.2 查询资方结果
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
public void repayResult(String repayId) {
    logger.info("还款结果查询入口:{}", repayId);
    BankRepayRecord bankRepayRecord = bankRepayRecordRepository.findById(repayId)
        .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
    CustomRepayRecord customRepayRecord = recordRepository.findById(bankRepayRecord.getSourceRecordId())
        .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));

    RepayQueryDto repayQueryDto = new RepayQueryDto();
    repayQueryDto.setOuterRepayId(repayId);
    repayQueryDto.setRepayId(bankRepayRecord.getBankRepayNo());

    logger.info("调用资方查询接口repayQueryDto:{}", JsonUtil.toJsonString(repayQueryDto));
    RestResult<RepayResultDto> restResult = finRepayService.queryResult(repayQueryDto);
    logger.info("调用资方查询接口restResult:{}", JsonUtil.toJsonString(restResult));

    RepayResultDto data = restResult.getData();
    ProcessStatus repayStatus = data.getStatus();

    if (ProcessStatus.FAIL.equals(repayStatus)) {
        failUpdate(bankRepayRecord, customRepayRecord, restResult);
    } else if (ProcessStatus.SUCCESS.equals(repayStatus)) {
        capitalSuccessUpdate(bankRepayRecord, restResult, replan, customRepayRecord);
    } else {
        mqService.submitRepayQueryDelay(repayId);
    }
}
````
</augment_code_snippet>

##### 4.3 成功时更新状态
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private void capitalSuccessUpdate(BankRepayRecord bankRepayRecord, RestResult<RepayResultDto> restResult,
                                  RepayPlan replan, CustomRepayRecord customRepayRecord) {
    logger.info("对资成功订单入口id:{}", bankRepayRecord.getId());
    bankRepayRecord.setState(ProcessState.SUCCEED);
    bankRepayRecord.setRepayTime(restResult.getData().getActRepayTime());
    bankRepayRecord.setBankRepayNo(restResult.getData().getRepayId());

    // 更新还款计划实际金额
    replan.setActPrincipalAmt(customRepayRecord.getPrincipalAmt());
    replan.setActGuaranteeAmt(customRepayRecord.getGuaranteeAmt());
    replan.setActPenaltyAmt(customRepayRecord.getPenaltyAmt());
    replan.setActCapitalPenaltyAmt(customRepayRecord.getCapitalPenaltyAmt());
    replan.setActAmount(customRepayRecord.getTotalAmt());
    replan.setActRepayTime(restResult.getData().getActRepayTime());

    bankRepayRecordRepository.save(bankRepayRecord);
    replan = repayPlanRepository.save(replan);
    customRepayRecord.setRepaidDate(restResult.getData().getActRepayTime());
    customRepayRecord = recordRepository.save(customRepayRecord);

    if (RepayMode.ONLINE == bankRepayRecord.getRepayMode()) {
        // 线上还款发布事件
        eventPublisher.publishEvent(new PrincipalRepaySucceedEvent(customRepayRecord.getId()));
    }
}
````
</augment_code_snippet>

##### 4.4 失败时更新状态
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private void failUpdate(BankRepayRecord bankRepayRecord, CustomRepayRecord customRepayRecord,
                       RestResult<RepayResultDto> restResult) {
    logger.info("失败订单入口id:{}", customRepayRecord.getId());
    bankRepayRecord.setFailReason(restResult.getData().getFailMsg());
    bankRepayRecord.setState(ProcessState.FAILED);
    customRepayRecord.setFailReason(restResult.getData().getFailMsg());
    customRepayRecord.setRepayState(ProcessState.FAILED);
    bankRepayRecordRepository.save(bankRepayRecord);
    recordRepository.save(customRepayRecord);

    // 还款失败回调
    CallBackDTO callBackDTO = new CallBackDTO();
    callBackDTO.setFlowChannel(loan.getFlowChannel());
    callBackDTO.setCallbackState(CallbackState.REPAY_FAIL);
    callBackDTO.setBusinessId(customRepayRecord.getId());
    mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
}
````
</augment_code_snippet>

#### 5. 成功后处理
```
资方成功 → 发布PrincipalRepaySucceedEvent → 更新状态 → 发布RepaySucceedResultEvent
```

**事件处理流程**:

##### 5.1 PrincipalRepaySucceedEvent 处理
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/event/listener/PrincipalRepaySucceedListener.java" mode="EXCERPT">
````java
@EventListener(PrincipalRepaySucceedEvent.class)
public void online(PrincipalRepaySucceedEvent principalSucceedEvent) {
    logger.info("处理本息成功事件:{}", JsonUtil.toJsonString(principalSucceedEvent));

    CustomRepayRecord customRepayRecord = customRepayRecordRepository
        .findById(principalSucceedEvent.getCustomRepayRecordId())
        .orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
    updateSuccessRecordAndPlan(customRepayRecord);
    eventPublisher.publishEvent(new RepaySucceedResultEvent(customRepayRecord.getId()));
}

private void updateSuccessRecordAndPlan(CustomRepayRecord repayRecord) {
    repayRecord.setRepayState(ProcessState.SUCCEED);
    customRepayRecordRepository.save(repayRecord);

    List<RepayPlan> planList = repayPlanRepository.findByLoanIdOrderByPeriod(repayRecord.getLoanId());
    final int curPeriod = repayRecord.getPeriod();
    RepayPurpose purpose = repayRecord.getRepayPurpose();

    RepayPlan curPlan = planList.stream()
        .filter(p -> p.getPeriod().compareTo(curPeriod) == 0)
        .findFirst().orElseThrow(() -> new BizException(ResultCode.REPAY_NOT_EXIST));
    curPlan.setCustRepayState(RepayState.REPAID);
    repayPlanRepository.save(curPlan);

    if (RepayPurpose.CLEAR == purpose) {
        // 结清时，将后续期次也标记为已还
        planList.stream().filter(p -> p.getPeriod() > curPeriod).forEach(p -> {
            p.setActAmount(BigDecimal.ZERO);
            p.setActRepayTime(curPlan.getActRepayTime());
            p.setCustRepayState(RepayState.REPAID);
            repayPlanRepository.save(p);
        });
    }
}
````
</augment_code_snippet>

##### 5.2 RepaySucceedResultEvent 处理
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/event/listener/RepaySucceedResultEventListener.java" mode="EXCERPT">
````java
@EventListener(RepaySucceedResultEvent.class)
public void onApplicationEvent(RepaySucceedResultEvent repaySucceedResultEvent) {
    CustomRepayRecord record = customRepayRecordRepository
        .findById(repaySucceedResultEvent.getCustomRecordId()).orElseThrow();

    Loan loan = loanRepository.findById(record.getLoanId()).orElseThrow();
    repayCallback(loan, record);
}

public void repayCallback(Loan loan, CustomRepayRecord customRepayRecord) {
    if (customRepayRecord.getRepayPurpose() == RepayPurpose.CLEAR ||
        Objects.equals(customRepayRecord.getPeriod(), loan.getPeriods())) {
        //订单更新为结清
        String orderId = loan.getOrderId();
        Order order = orderRepository.findById(orderId).orElseThrow();
        order.setOrderState(OrderState.CLEAR);
        orderRepository.save(order);

        //结清回调
        CallBackDTO clearCallBackDTO = new CallBackDTO();
        clearCallBackDTO.setFlowChannel(loan.getFlowChannel());
        clearCallBackDTO.setCallbackState(CallbackState.CLEAR);
        clearCallBackDTO.setBusinessId(orderId);
        mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(clearCallBackDTO));
    }

    //还款成功回调
    CallBackDTO callBackDTO = new CallBackDTO();
    callBackDTO.setFlowChannel(loan.getFlowChannel());
    callBackDTO.setCallbackState(CallbackState.REPAID);
    callBackDTO.setBusinessId(customRepayRecord.getId());
    mqService.submitCallbackCommonNotify(JsonUtil.toJsonString(callBackDTO));
}
````
</augment_code_snippet>

### 线下还款流程

#### 1. 线下销账申请
```
管理端发起 → RepayService.offline()
```

**主要差异**:
- 支持减免申请的使用
- 可以指定还款时间
- 需要人工审核通过

##### 1.1 线下还款入口
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
public String offline(OfflineRepayApplyRequest request) {
    logger.info("线下销账请求:{}", JsonUtil.toJsonString(request));

    Loan loan = loanRepository.findByOrderId(request.getOrderId());
    String loanId = loan.getId();
    RepayPurpose repayPurpose = request.getRepayPurpose();

    // 查询审批通过待审核的减免
    OfflineRepayReduce repayReduce;
    if (StringUtil.isBlank(request.getReductNo())) {
        repayReduce = offlineRepayReduceRepository
            .findByLoanIdAndPeriodAndRepayPurposeAndAuditStateAndUseState(
                loanId, period, repayPurpose, AuditStatus.PASS, UseState.WAIT);
    } else {
        repayReduce = offlineRepayReduceRepository
            .findByOrderIdAndAuditStateAndUseState(request.getReductNo(), AuditStatus.PASS, UseState.WAIT);
    }

    // 还款试算
    TrialResultVo trialResultVo = trial(loanId, repayPurpose, period, request.getRepayDate());

    OfflineRepayApply repayApply;
    if (repayReduce != null) {
        repayApply = ManageConvert.INSTANCE.toOfflineRepayApply(repayReduce);
        processOfflineRepayApply(repayApply);
        // 更新减免为已使用
        repayReduce.setUseState(UseState.USED);
        offlineRepayReduceRepository.save(repayReduce);
    } else {
        repayApply = initOfflineRepayApply(trialResultVo, request, loan);
    }

    repayApply = offlineRepayApplyRepository.save(repayApply);

    // 初始化还款记录
    BankRepayRecord bankRepayRecord = initOfflineRepayRecord(repayApply, trialResultVo, loan, request.getOuterRepayNo());
    // 通知core
    mqService.submitRepay(bankRepayRecord.getId());

    return bankRepayRecord.getId();
}
````
</augment_code_snippet>

## 状态枚举说明

### ProcessState (处理状态)
- `INIT`: 初始状态
- `PROCESSING`: 处理中
- `SUCCEED`: 成功 (终态)
- `FAILED`: 失败 (终态)

### RepayState (还款状态)
- `NORMAL`: 正常未还
- `REPAID`: 已还款

### RepayPurpose (还款目的)
- `CURRENT`: 当期还款
- `CLEAR`: 提前结清

### RepayMode (还款模式)
- `ONLINE`: 线上还款
- `OFFLINE`: 线下还款

## 消息队列处理

### 还款相关队列
1. **REPAY_APPLY**: 还款申请队列
   - 处理器: `RepayListener.listenRepayApply()`
   - 调用: `RepayService.repay()`

2. **REPAY_QUERY_DELAY**: 还款结果查询延迟队列
   - 处理器: `RepayResultListener.listenRepayResult()`
   - 调用: `RepayService.repayResult()`

3. **CALLBACK_COMMON_NOTIFY**: 回调通知队列
   - 用于向流量方发送还款结果通知

## 异常处理机制

### 1. 重复提交防护
- 使用分布式锁 `cash_business_repay_{loanId}`
- 锁定时间: 20秒

### 2. 异常重试
- 资方调用异常时，仍会发送延迟查询消息
- 通过MQ延迟消息实现自动重试

### 3. 状态一致性检查
- 宝付与资方状态不一致时发出预警
- 继续延迟查询直到状态一致

## 关键业务规则

### 1. 减免规则 (仅LVXIN渠道)
- 逾期且非宽限期内才能减免
- 罚息减免不能超过平台收取部分 (对客罚息-对资罚息)
- 咨询费可全额减免

### 2. 宽限期计算
- 不同银行渠道有不同宽限期
- HXBK在年结期间会增加宽限期天数

### 3. 结清处理
- 结清时会将后续所有期次标记为已还
- 更新订单状态为结清
- 发送结清回调通知

## 重要配置和常量

### 1. 锁相关配置
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
public static final int LOCK_WAIT_SECOND = 3;
public static final int LOCK_RELEASE_SECOND = 8;
public static final int REPAY_LOCK_RELEASE_SECOND = 20;
````
</augment_code_snippet>

### 2. 减免顺序配置
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
/**
 * 默认减免顺序
 */
private static final List<ReduceItem> DEFAULT_REDUCE_ORDER = Arrays.asList(
    ReduceItem.PENALTY,
    ReduceItem.CONSULT_FEE,
    ReduceItem.GUARANTEE,
    ReduceItem.INTEREST,
    ReduceItem.PRINCIPAL
);

/**
 * 拍拍减免顺序
 */
private static final List<ReduceItem> PPD_REDUCE_ORDER = Arrays.asList(
    ReduceItem.CONSULT_FEE,
    ReduceItem.PENALTY
);
````
</augment_code_snippet>

### 3. MQ队列配置
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/config/RabbitConfig.java" mode="EXCERPT">
````java
public interface Queues {
    String REPAY_APPLY = "repay.apply";
    String REPAY_QUERY = "repay.query";
    String REPAY_QUERY_DELAY = "repay.query.delay";
    String REPAY_NOTIFY = "repay.notify";
    String REPAY_NOTIFY_DELAY = "repay.notify.delay";
    String REPAY_NOTIFY_RESULT_DELAY = "repay.notify.result.delay";
    String CHARGE_APPLY = "charge.apply";
    String CHARGE_QUERY_DELAY = "charge.query.delay";
}

public interface RoutingKeys {
    String REPAY_APPLY = "repay.apply";
    String REPAY_QUERY_DELAY = "repay.query.delay";
    String REPAY_NOTIFY = "repay.notify";
    String REPAY_NOTIFY_DELAY = "repay.notify.delay";
    String REPAY_NOTIFY_RESULT_DELAY = "repay.notify.result.delay";
    String CHARGE_APPLY = "charge.apply";
    String CHARGE_QUERY_DELAY = "charge.query.delay";
}
````
</augment_code_snippet>

### 4. 系统维护配置
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
@Value("${repay.maintain.start}")
private String repayMaintainStart;

@Value("${repay.maintain.end}")
private String repayMaintainEnd;

@Value("${repay.maintain.msg}")
private String repayMaintainMsg;

@Value("${hxbk.overDueDayAdd}")
private Integer hxbkOverDueDayAdd;
````
</augment_code_snippet>

## 监控告警

### 1. 状态不一致告警
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
WithholdFlow withholdFlow = withholdFlowRepository.findByRepayRecordId(customRepayRecord.getId());
if (withholdFlow != null) {
    String withholdFlowStatus = withholdFlow.getPayState().name();
    //宝付、资方状态不一致预警
    if (!withholdFlowStatus.startsWith(repayStatus.name().substring(0, 4))
        && (ProcessStatus.FAIL.equals(repayStatus) || ProcessStatus.SUCCESS.equals(repayStatus))) {
        warningService.warn(repayId + "还款结果查询：宝付、资方状态不一致,宝付状态：" + withholdFlowStatus + "，资方状态：" + repayStatus.name());
        mqService.submitRepayQueryDelay(repayId);
        return;
    }
}
````
</augment_code_snippet>

### 2. 业务异常告警
- 还款申请失败
- 资方接口调用异常
- 状态更新失败

## 流程图

### 还款流程状态图

```mermaid
graph TD
    A[用户发起还款] --> B{系统维护检查}
    B -->|维护中| C[返回维护提示]
    B -->|正常| D[获取锁]

    D --> E{获取锁成功?}
    E -->|失败| F[返回重复提交错误]
    E -->|成功| G[校验还款记录]

    G --> H{存在处理中记录?}
    H -->|是| I[返回处理中错误]
    H -->|否| J[创建CustomRepayRecord]

    J --> K[还款试算]
    K --> L{试算成功?}
    L -->|失败| M[更新记录为失败]
    L -->|成功| N{LVXIN渠道?}

    N -->|是| O[减免校验]
    N -->|否| P[创建BankRepayRecord]

    O --> Q{减免校验通过?}
    Q -->|否| R[更新记录为失败]
    Q -->|是| P

    P --> S[发送MQ消息]
    S --> T[MQ处理还款申请]

    T --> U[更新状态为PROCESSING]
    U --> V[调用资方接口]
    V --> W[发送延迟查询消息]

    W --> X[MQ处理结果查询]
    X --> Y[查询资方结果]
    Y --> Z{资方处理结果}

    Z -->|成功| AA[更新BankRepayRecord为SUCCEED]
    Z -->|失败| BB[更新记录为FAILED]
    Z -->|处理中| CC[继续延迟查询]

    AA --> DD[发布PrincipalRepaySucceedEvent]
    DD --> EE[更新CustomRepayRecord为SUCCEED]
    EE --> FF[更新RepayPlan为REPAID]
    FF --> GG{结清还款?}

    GG -->|是| HH[更新后续期次为REPAID]
    GG -->|否| II[发布RepaySucceedResultEvent]
    HH --> II

    II --> JJ[发送还款成功回调]
    JJ --> KK{结清或最后一期?}
    KK -->|是| LL[更新订单为CLEAR]
    KK -->|否| MM[流程结束]
    LL --> NN[发送结清回调]
    NN --> MM

    BB --> OO[发送还款失败回调]
    CC --> X

    style A fill:#e1f5fe
    style MM fill:#c8e6c9
    style C fill:#ffcdd2
    style F fill:#ffcdd2
    style I fill:#ffcdd2
    style M fill:#ffcdd2
    style R fill:#ffcdd2
    style BB fill:#ffcdd2
    style OO fill:#ffcdd2
```

### 还款表状态变化时序图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Flow as Flow系统
    participant MQ as 消息队列
    participant Capital as Capital系统
    participant Bank as 资方银行

    Note over Flow: CustomRepayRecord: INIT<br/>BankRepayRecord: -<br/>RepayPlan: NORMAL

    User->>Flow: 发起还款请求
    Flow->>Flow: 创建CustomRepayRecord(INIT)
    Flow->>Flow: 还款试算和校验
    Flow->>Flow: 创建BankRepayRecord(INIT)
    Flow->>MQ: 发送还款申请消息

    Note over Flow: CustomRepayRecord: INIT<br/>BankRepayRecord: INIT<br/>RepayPlan: NORMAL

    MQ->>Flow: 处理还款申请
    Flow->>Flow: 更新CustomRepayRecord(PROCESSING)
    Flow->>Capital: 调用资方还款接口
    Capital->>Bank: 发起扣款请求
    Flow->>Flow: 更新BankRepayRecord(PROCESSING)
    Flow->>MQ: 发送延迟查询消息

    Note over Flow: CustomRepayRecord: PROCESSING<br/>BankRepayRecord: PROCESSING<br/>RepayPlan: NORMAL

    Bank-->>Capital: 扣款结果(成功)

    MQ->>Flow: 处理结果查询
    Flow->>Capital: 查询还款结果
    Capital-->>Flow: 返回成功结果
    Flow->>Flow: 更新BankRepayRecord(SUCCEED)
    Flow->>Flow: 更新RepayPlan实际还款信息

    Note over Flow: CustomRepayRecord: PROCESSING<br/>BankRepayRecord: SUCCEED<br/>RepayPlan: NORMAL(更新实际金额)

    Flow->>Flow: 发布PrincipalRepaySucceedEvent
    Flow->>Flow: 更新CustomRepayRecord(SUCCEED)
    Flow->>Flow: 更新RepayPlan(REPAID)

    alt 结清还款
        Flow->>Flow: 更新后续期次RepayPlan(REPAID)
    end

    Note over Flow: CustomRepayRecord: SUCCEED<br/>BankRepayRecord: SUCCEED<br/>RepayPlan: REPAID

    Flow->>Flow: 发布RepaySucceedResultEvent
    Flow->>MQ: 发送还款成功回调

    alt 结清或最后一期
        Flow->>Flow: 更新Order(CLEAR)
        Flow->>MQ: 发送结清回调
    end

    MQ-->>User: 还款成功通知
```

## 详细状态变化时序

### 线上还款成功场景
```
时间点 | CustomRepayRecord | BankRepayRecord | RepayPlan | 操作
-----|------------------|-----------------|-----------|-----
T1   | INIT             | -               | NORMAL    | 创建对客记录
T2   | INIT             | INIT            | NORMAL    | 创建对资记录，发送MQ
T3   | PROCESSING       | PROCESSING      | NORMAL    | 资方扣款中
T4   | PROCESSING       | SUCCEED         | NORMAL    | 资方扣款成功
T5   | SUCCEED          | SUCCEED         | REPAID    | 事件处理完成
```

### 线上还款失败场景
```
时间点 | CustomRepayRecord | BankRepayRecord | RepayPlan | 操作
-----|------------------|-----------------|-----------|-----
T1   | INIT             | -               | NORMAL    | 创建对客记录
T2   | INIT             | INIT            | NORMAL    | 创建对资记录，发送MQ
T3   | PROCESSING       | PROCESSING      | NORMAL    | 资方扣款中
T4   | FAILED           | FAILED          | NORMAL    | 资方扣款失败
```

### 提前结清成功场景
```
时间点 | CustomRepayRecord | BankRepayRecord | RepayPlan(当期) | RepayPlan(后续期) | Order
-----|------------------|-----------------|----------------|------------------|-------
T1   | INIT             | -               | NORMAL         | NORMAL           | LOAN_PASS
T2   | INIT             | INIT            | NORMAL         | NORMAL           | LOAN_PASS
T3   | PROCESSING       | PROCESSING      | NORMAL         | NORMAL           | LOAN_PASS
T4   | PROCESSING       | SUCCEED         | NORMAL         | NORMAL           | LOAN_PASS
T5   | SUCCEED          | SUCCEED         | REPAID         | REPAID           | CLEAR
```

## 关键业务逻辑代码

### 1. 减免校验逻辑 (LVXIN渠道)
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private boolean onlineExemptionInspection(RepayPlan repayPlan, CustomRepayRecord customRepayRecord,
                                         OnlineRepayRequestDto request, BigDecimal capitalPenalty) {
    boolean flag = false;
    //如果减免罚息字段有值，则得保证减免咨询费必须和咨询费额度相同
    //如罚息减免不超过对客罚息-对资罚息
    BigDecimal subtract = repayPlan.getPenaltyAmt().subtract(capitalPenalty);

    //减免金额=减免罚息+减免咨询费
    customRepayRecord.setReduceAmount(request.getPenaltyInterestWaiver().add(request.getConsultationFeeWaiver()));
    //实还罚息=应还罚息-减免罚息
    customRepayRecord.setPenaltyAmt(repayPlan.getPenaltyAmt().subtract(request.getPenaltyInterestWaiver()));
    //实还咨询费=应还咨询费-减免咨询费
    customRepayRecord.setConsultFee(repayPlan.getConsultFee().subtract(request.getConsultationFeeWaiver()));
    //平台罚息=对客-对资-减免罚息
    customRepayRecord.setActPlatformPenaltyAmt(subtract.subtract(request.getPenaltyInterestWaiver()));

    if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) > 0) &&
        request.getConsultationFeeWaiver().compareTo(repayPlan.getConsultFee()) == 0 &&
        request.getPenaltyInterestWaiver().compareTo(subtract) <= 0) {
        customRepayRecord.setRemark("减免成功");
        flag = true;
    } else if ((request.getPenaltyInterestWaiver().compareTo(BigDecimal.ZERO) == 0) &&
        request.getConsultationFeeWaiver().compareTo(repayPlan.getConsultFee()) <= 0) {
        customRepayRecord.setRemark("减免成功");
        flag = true;
    } else {
        customRepayRecord.setRepayState(ProcessState.FAILED);
        customRepayRecord.setRemark("减免超限制");
        flag = false;
    }
    return flag;
}
````
</augment_code_snippet>

### 2. 宽限期判断逻辑
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
//判断是否未逾期，和 不能有减免
boolean isGracePeriod(RepayPlan repayPlan, LocalDate date, Loan loan) {
    Boolean flag = false;
    //逾期天数
    long overDay = DateUtil.dateDiff(repayPlan.getPlanRepayDate(), date);
    //宽限期
    int graceDay = QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay();
    if (loan.getBankChannel() == BankChannel.HXBK) {
        //宽限期 年结期间，湖消的逾期天数会增加
        graceDay = hxbkOverDueDayAdd == null ?
            QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay() :
            QhBank.getQhBankBy(loan.getBankChannel()).getGraceDay() + hxbkOverDueDayAdd;
    }
    logger.info("宽限期:{},逾期天数:{},当前时间:{}", overDay, graceDay, repayPlan.getPlanRepayDate().isAfter(date));
    if (overDay <= graceDay || repayPlan.getPlanRepayDate().isAfter(date)) {
        flag = true;
    }
    return flag;
}
````
</augment_code_snippet>

### 3. 构建资方还款参数
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
private void buildRepayApply(RepayApplyDto repayApplyDto, BankRepayRecord bankRepayRecord,
                           Loan loan, UserBankCard userBankCard) {
    repayApplyDto.setOuterLoanId(Optional.ofNullable(loan.getLoanRecordId()).orElse(loan.getId()));
    repayApplyDto.setLoanId(loan.getLoanNo());
    repayApplyDto.setOuterRepayId(bankRepayRecord.getId());
    repayApplyDto.setAmount(bankRepayRecord.getAmount());
    repayApplyDto.setPeriod(bankRepayRecord.getPeriod());
    repayApplyDto.setRepayPurpose(com.jinghang.capital.api.dto.repay.RepayPurpose.valueOf(bankRepayRecord.getRepayPurpose().name()));
    repayApplyDto.setRepayMode(bankRepayRecord.getRepayMode() == RepayMode.ONLINE ?
        com.jinghang.capital.api.dto.repay.RepayMode.ONLINE : com.jinghang.capital.api.dto.repay.RepayMode.OFFLINE);

    repayApplyDto.setPrincipal(bankRepayRecord.getPrincipal());
    repayApplyDto.setInterest(bankRepayRecord.getInterest());
    repayApplyDto.setGuaranteeFee(bankRepayRecord.getGuarantee());
    repayApplyDto.setOverdueFee(bankRepayRecord.getPenalty());
    repayApplyDto.setPlatformOverdueFee(bankRepayRecord.getPlatformPenalty());
    repayApplyDto.setConsultFee(bankRepayRecord.getConsultFee());
    repayApplyDto.setBreachFee(bankRepayRecord.getBreach());

    // 绑卡信息
    if (Objects.nonNull(userBankCard)) {
        repayApplyDto.setRepayRelUser(userBankCard.getCardName());
        repayApplyDto.setRepayBankCode(userBankCard.getBankCode());
        repayApplyDto.setAgreementNo(userBankCard.getAgreeNo());
        repayApplyDto.setRepayAcctNo(userBankCard.getCardNo());
        repayApplyDto.setRepayRelCard(userBankCard.getCertNo());
        repayApplyDto.setRepayRelPhone(userBankCard.getPhone());
    }
}
````
</augment_code_snippet>

## 关键代码位置总结

### 1. 还款入口
- **线上还款**: `RepayService.online(OnlineRepayRequestDto)`
- **线下还款**: `RepayService.offline(OfflineRepayApplyRequest)`

### 2. 状态更新关键方法
- **资方成功处理**: `RepayService.capitalSuccessUpdate()`
- **失败处理**: `RepayService.failUpdate()`
- **事件监听**: `PrincipalRepaySucceedListener.online()`

### 3. MQ消息处理
- **还款申请**: `RepayListener.listenRepayApply()`
- **结果查询**: `RepayResultListener.listenRepayResult()`

## 数据库表关系

```
Loan (借据)
  ├── RepayPlan (还款计划) [1:N]
  └── CustomRepayRecord (对客还款记录) [1:N]
       └── BankRepayRecord (对资还款记录) [1:1]

Order (订单)
  └── Loan (借据) [1:1]
```

## 调试和问题排查

### 1. 常见问题排查步骤

#### 还款卡住不动
1. **检查对客还款记录状态**
   ```sql
   SELECT * FROM custom_repay_record WHERE loan_id = 'xxx' AND period = 1 ORDER BY created_time DESC;
   ```

2. **检查对资还款记录状态**
   ```sql
   SELECT * FROM bank_repay_record WHERE loan_id = 'xxx' AND period = 1 ORDER BY created_time DESC;
   ```

3. **检查MQ消息是否正常处理**
   - 查看RabbitMQ管理界面
   - 检查队列积压情况
   - 查看错误日志

#### 状态不一致问题
1. **检查还款计划状态**
   ```sql
   SELECT * FROM repay_plan WHERE loan_id = 'xxx' ORDER BY period;
   ```

2. **检查宝付扣款记录**
   ```sql
   SELECT * FROM withhold_flow WHERE repay_record_id = 'xxx';
   ```

### 2. 关键日志位置

#### 还款申请日志
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
logger.info("线上还款请求:{}", JsonUtil.toJsonString(request));
logger.info("对客还款记录id:{}", record.getId());
logger.info("还款申请,core param:{}", JsonUtil.toJsonString(repayApplyDto));
logger.info("还款申请,core返回:{}", JsonUtil.toJsonString(restResult));
````
</augment_code_snippet>

#### 结果查询日志
<augment_code_snippet path="jh-loan-cash-flow/src/main/java/com/maguo/loan/cash/flow/service/RepayService.java" mode="EXCERPT">
````java
logger.info("还款结果查询入口:{}", repayId);
logger.info("调用资方查询接口repayQueryDto:{}", JsonUtil.toJsonString(repayQueryDto));
logger.info("调用资方查询接口restResult:{}", JsonUtil.toJsonString(restResult));
logger.info("对资成功订单入口id:{}", bankRepayRecord.getId());
logger.info("失败订单入口id:{}", customRepayRecord.getId());
````
</augment_code_snippet>

### 3. 手动修复数据脚本示例

#### 修复卡住的还款记录
```sql
-- 1. 查询卡住的记录
SELECT crr.id, crr.repay_state, brr.id as bank_id, brr.state as bank_state, rp.cust_repay_state
FROM custom_repay_record crr
LEFT JOIN bank_repay_record brr ON brr.source_record_id = crr.id
LEFT JOIN repay_plan rp ON rp.loan_id = crr.loan_id AND rp.period = crr.period
WHERE crr.loan_id = 'xxx' AND crr.period = 1;

-- 2. 手动更新状态（谨慎操作）
UPDATE custom_repay_record SET repay_state = 'SUCCEED' WHERE id = 'xxx';
UPDATE bank_repay_record SET state = 'SUCCEED' WHERE id = 'xxx';
UPDATE repay_plan SET cust_repay_state = 'REPAID' WHERE loan_id = 'xxx' AND period = 1;
```

### 4. 性能监控指标

#### 关键指标
- 还款成功率
- 平均处理时间
- MQ消息积压数量
- 资方接口响应时间
- 状态不一致告警数量

#### 监控SQL
```sql
-- 今日还款成功率
SELECT
    COUNT(*) as total,
    SUM(CASE WHEN repay_state = 'SUCCEED' THEN 1 ELSE 0 END) as success,
    SUM(CASE WHEN repay_state = 'SUCCEED' THEN 1 ELSE 0 END) * 100.0 / COUNT(*) as success_rate
FROM custom_repay_record
WHERE DATE(created_time) = CURDATE();

-- 处理中超过1小时的记录
SELECT * FROM custom_repay_record
WHERE repay_state = 'PROCESSING'
AND created_time < DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

## 总结

还款流程是一个复杂的异步处理过程，涉及多个系统的交互：
1. **Flow系统**: 处理用户还款请求，管理还款状态
2. **Capital系统**: 与资方对接，处理实际扣款
3. **支付系统**: 处理用户银行卡扣款
4. **MQ系统**: 保证异步处理的可靠性

整个流程通过状态机模式管理，确保数据一致性和业务完整性。关键的设计原则包括：
- **幂等性**: 通过分布式锁和状态检查防止重复处理
- **可靠性**: 通过MQ延迟消息实现自动重试
- **一致性**: 通过事件驱动模式保证多表状态同步
- **可观测性**: 通过日志和告警及时发现异常

通过本文档的详细代码分析，您可以：
1. 快速定位还款流程中的关键节点
2. 理解各个状态的变化逻辑
3. 掌握问题排查的方法和工具
4. 了解系统的监控和告警机制

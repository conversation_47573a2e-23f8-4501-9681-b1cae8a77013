# 🏦 鲸航金融贷款系统业务流程说明文档

## 📋 文档概述

本文档详细说明鲸航金融贷款系统的各个核心业务流程，包括授信流程、用信流程、还款流程和贷后管理流程。适用于产品经理、业务人员、运营人员、客服人员等所有岗位理解业务全貌。

---

## 🎯 1. 授信流程 (Credit Process)

### 1.1 流程概述
授信是指资方或金融机构根据用户的信用状况，预先给予用户一定的借款额度。用户在额度范围内可以随时申请借款。目前本系统仅支持单笔单批，既授信额度与用信金额一致，不可更改。

### 1.2 详细流程步骤

#### 第一步：用户申请提交
- **操作主体**：用户
- **业务内容**：
  - 用户通过各个流量渠道（如绿信、拍拍等）提交借款申请
  - 填写基本信息：姓名、身份证号、手机号、居住地址等
  - 上传身份证正反面照片和活体人脸照片
  - 填写联系人信息和工作信息

#### 第二步：系统初步验证
- **操作主体**：系统自动处理
- **业务内容**：
  - 检查用户是否已有在途订单（防止重复申请）
  - 验证身份证信息的真实性和有效性
  - 进行OCR识别，提取身份证信息（此步骤渠道已实现，本系统无该功能）
  - 人脸识别验证，确保本人操作（仅使用渠道传来的人脸信用分，如果没有则使用默认值）

#### 第三步：风控审核
- **操作主体**：风控系统
- **业务内容**：
  - **内部风控**：基于平台自有的风控规则进行评估
  - **外部风控**：调用第三方风控服务获取用户征信信息
  - **黑名单检查**：验证用户是否在黑名单中
  - **多头借贷检查**：防止用户在多个平台同时借款
- **可能结果**：
  - ✅ **通过**：进入下一步
  - ❌ **拒绝**：申请结束，本系统终态返回渠道
  - ⏸️ **挂起**：需要人工审核

#### 第四步：资金方路由
- **操作主体**：系统自动处理
- **业务内容**：
  - 检查资金方的日限额和时间窗口
  - 目前所有项目都为直连资方，无路由功能

#### 第五步：资金方授信申请
- **操作主体**：系统对接资金方
- **业务内容**：
  - 向设定好的资金方发起授信申请
  - 提交用户资料
  - 资方进行内部审核和风控
  - 等待资方返回授信结果
- **可能结果**：
  - ✅ **授信通过**：获得借款额度，进入协议签署
  - ❌ **授信失败**：返回失败终态到渠道
  - ⏸️ **处理中**：继续等待资方审核

#### 第六步：协议签署
- **操作主体**：用户 + 系统
- **业务内容**：
  - 系统生成相关法律协议（借款协议、担保协议等）
  - 用户进行电子签名确认（部分协议由渠道或资方进行签署，本系统只需要获取留存）
  - 协议存档备查
- **重要说明**：只有协议签署完成后，用户才能进行实际借款

### 1.3 授信结果
- **授信成功**：用户获得借款额度，可以申请用信
- **授信失败**：申请结束，30天内不能重新申请（冷静期）

---

## 💰 2. 用信流程 (Loan Process)

### 2.1 流程概述
用信是指用户在已获得的授信额度范围内，申请实际借款并获得资金的过程。

### 2.2 详细流程步骤

#### 第一步：用户发起借款申请
- **操作主体**：用户
- **业务内容**：
  - 用户在授信额度内选择借款金额和期数（单笔单扣模式下授信额度与借款金额需一致）
  - 确认借款用途和还款方式
  - 选择或绑定收款资方卡

#### 第二步：放款前检查
- **操作主体**：系统自动处理
- **业务内容**：
  - **黑暗期校验**：校验借款时间是否为黑暗期
  - **在途订单检查**：确保用户没有其他正在处理的借款

#### 第三步：生成还款计划
- **操作主体**：系统自动处理
- **业务内容**：
  - 根据借款金额、期数、利率计算每期还款金额
  - 生成详细的还款计划表
  - 包含本金、利息、担保费、咨询费等明细
  - 确定每期的还款日期

#### 第四步：资金方放款
- **操作主体**：资方系统
- **业务内容**：
  - 向资方发起放款申请
  - 资方进行最终审核
  - 资方将资金转入用户指定账户
  - 系统接收放款结果通知
- **可能结果**：
  - ✅ **放款成功**：用户收到借款资金
  - ❌ **放款失败**：申请结束，通知用户失败原因

#### 第五步：放款成功处理
- **操作主体**：系统自动处理
- **业务内容**：
  - 更新订单状态为"放款成功"
  - 激活还款计划，开始计息
  - 发送放款成功通知给用户
  - 开始贷后管理流程

### 2.3 用信结果
- **用信成功**：用户收到借款资金，开始按期还款
- **用信失败**：申请结束，授信额度保留可再次申请

---

## 💳 3. 还款流程 (Repayment Process)

### 3.1 流程概述
还款是指用户按照还款计划，定期偿还借款本金和利息的过程。

### 3.2 还款方式

#### 3.2.1 线上还款（主动还款）
- **操作主体**：用户主动发起
- **适用场景**：用户主动提前还款或正常还款
- **操作方式**：通过APP、网站等渠道发起还款

#### 3.2.2 自动扣款（被动还款） 批扣
- **操作主体**：资方系统自动发起
- **适用场景**：到期日自动从用户绑定的还款卡扣款
- **操作方式**：系统定时任务触发

#### 3.2.3 线下还款（人工处理）
- **操作主体**：客服或运营人员
- **适用场景**：特殊情况下的人工销账
- **操作方式**：通过管理后台操作

### 3.3 详细流程步骤

#### 第一步：还款发起
- **线上还款**：用户选择还款期数和金额
- **自动扣款**：系统在还款日自动触发
- **线下还款**：客服人员在后台发起

#### 第二步：还款试算
- **操作主体**：系统自动处理
- **业务内容**：
  - 计算当期应还金额（本金+利息+费用）
  - 如有逾期，计算逾期罚息
  - 支持减免计算（特定渠道）
  - 生成详细的还款明细

#### 第三步：扣款处理
- **操作主体**：支付系统 + 资方
- **业务内容**：
  - 从用户绑定的还款资方卡扣款
  - 实时获取扣款结果
  - 处理扣款成功或失败的情况

#### 第四步：还款结果处理
- **扣款成功**：
  - 更新还款计划状态为"已还款"
  - 更新实际还款时间和金额
  - 发送还款成功通知
  - 如为最后一期或提前结清，更新订单状态为"结清"
- **扣款失败**：
  - 记录失败原因
  - 发送还款失败通知
  - 可能触发催收流程

### 3.4 特殊还款场景

#### 3.4.1 提前结清
- **定义**：用户一次性还清所有剩余欠款
- **处理结果**：所有后续期次标记为已还款，订单状态更新为结清

#### 3.4.2 逾期还款
- **定义**：超过还款日仍未还款
- **宽限期**：不同资方有不同的宽限期（通常1-3天）
- **逾期费用**：产生逾期罚息和违约金
- **催收流程**：触发人工催收

#### 3.4.3 减免还款
- **适用条件**：仅支持减免融担咨询费与罚息，资方不允许减免

---

## 📊 5. 业务数据流转

### 5.1 订单状态流转
```
初始化 → 风控通过 → 授信中 → 授信通过 → 放款中 → 放款通过 → 结清
   ↓         ↓        ↓        ↓       ↓        ↓
 风控拒绝   授信失败   放款失败   放款取消
```

### 5.2 还款状态流转
```
正常 → 逾期 → 催收中 → 已还款
  ↓      ↓      ↓
提前还款  减免还款  强制结清
```

---

## ⚠️ 6. 重要业务规则

### 6.1 时间控制规则
- **冷静期**：风控拒绝后30天内不能重新申请
- **黑暗期**：不同资金方有不同的业务时间限制
- **宽限期**：还款日后的宽限期内不算逾期

### 6.2 金额控制规则
- **单笔限额**：每个资金方有单笔借款金额限制
- **日限额**：每个资金方有日放款总额限制

### 6.3 风险控制规则
- **多头借贷**：防止用户同时在多个平台借款
- **频繁申请**：限制用户短期内频繁申请
- **异常行为**：监控和识别异常的借款和还款行为
- **黑名单机制**：对高风险用户进行限制

---

## 📋 10. 总结

本文档详细介绍了金融贷款系统的四大核心业务流程：授信、用信、还款和贷后管理。每个流程都有其特定的业务逻辑和风险控制点。理解这些流程有助于：

1. **提升工作效率**：各岗位人员能够更好地理解业务全貌
2. **优化用户体验**：通过流程优化提升用户满意度
3. **控制业务风险**：在各个环节设置合适的风险控制措施
4. **提高协作效率**：不同部门之间的协作更加顺畅

希望本文档能够帮助所有相关人员更好地理解和参与金融贷款业务的各个环节。

# HXBK影像件上传日志记录功能实现文档

## 概述

本文档记录了为HXBK影像件上传功能添加日志记录的实现过程。当影像件上传成功后，每一个文件上传成功都需要记录到`hxbk_ant_file_log`表中。

## 实现内容

### 1. 创建HXBKAntFileLogRepository

创建了新的Repository接口用于操作`hxbk_ant_file_log`表：

**文件路径**: `capital-core/src/main/java/com/jinghang/capital/core/repository/HXBKAntFileLogRepository.java`

```java
public interface HXBKAntFileLogRepository extends JpaRepository<HXBKAntFileLog, String> {
    List<HXBKAntFileLog> findByCreditId(String creditId);
    Optional<HXBKAntFileLog> findByCreditIdAndFileName(String creditId, String fileName);
    Optional<HXBKAntFileLog> findByFilePath(String filePath);
}
```

### 2. 修改CommonService

在`CommonService`类中添加了对`HXBKAntFileLogRepository`的支持：

- 添加了`@Autowired`注入
- 添加了`getHXBKAntFileLogRepository()`方法
- 添加了相应的import语句

### 3. 修改HXBKImageFileService

#### 3.1 修改uploadToSftp方法

- **原方法签名**: `uploadToSftp(Path filePath, String remoteDir, String fileName)`
- **新方法签名**: `uploadToSftp(Path filePath, String remoteDir, String fileName, String creditId)`

#### 3.2 添加saveFileUploadLog方法

新增了专门用于保存文件上传日志的方法：

```java
private void saveFileUploadLog(String creditId, String filePath, String fileName) {
    try {
        HXBKAntFileLog fileLog = new HXBKAntFileLog();
        fileLog.setCreditId(creditId);
        fileLog.setFilePath(filePath);
        fileLog.setFileName(fileName);
        fileLog.setRemark("HXBK影像件上传到蚂蚁SFTP");

        getCommonService().getHXBKAntFileLogRepository().save(fileLog);
        logger.info("保存文件上传日志成功, creditId: {}, fileName: {}", creditId, fileName);
    } catch (Exception e) {
        logger.error("保存文件上传日志失败, creditId: {}, fileName: {}", creditId, fileName, e);
    }
}
```

#### 3.3 修改调用点

修改了以下两个方法中对`uploadToSftp`的调用：

1. **uploadIdCardImagesAndBuildMaterials方法**:
   - `uploadToSftp(zipFilePath, remoteDir, zipFileName, applySerialNo)`
   - `uploadToSftp(indexFilePath, remoteDir, indexFileName, applySerialNo)`

2. **uploadFaceImagesAndBuildMaterials方法**:
   - `uploadToSftp(zipFilePath, remoteDir, zipFileName, applySerialNo)`
   - `uploadToSftp(indexFilePath, remoteDir, indexFileName, applySerialNo)`

## 功能特点

### 1. 自动记录
每当文件上传成功后，系统会自动将文件信息记录到数据库中，无需手动调用。

### 2. 异常处理
文件日志记录失败不会影响主要的文件上传流程，只会记录错误日志。

### 3. 详细信息记录
记录的信息包括：
- `creditId`: 授信ID（对应申请流水号）
- `filePath`: 完整的SFTP文件路径
- `fileName`: 文件名
- `remark`: 备注信息（固定为"HXBK影像件上传到蚂蚁SFTP"）

### 4. 支持的文件类型
系统会记录以下类型的文件上传：
- 身份证影像件ZIP文件（IDCARD-*.zip）
- 身份证影像件索引文件（IDCARD-*.zip.txt）
- 人脸影像件ZIP文件（PHOTO-*.zip）
- 人脸影像件索引文件（PHOTO-*.zip.txt）

## 数据库表结构

使用的实体类：`HXBKAntFileLog`

对应数据库表：`hxbk_ant_file_log`

主要字段：
- `id`: 主键（自动生成）
- `credit_id`: 授信ID
- `file_path`: 文件路径（SFTP地址）
- `file_name`: 文件名称
- `remark`: 备注
- 其他BaseEntity字段（创建时间、更新时间等）

## 使用场景

1. **监控文件上传状态**: 可以通过查询数据库了解哪些文件已经成功上传
2. **问题排查**: 当出现文件上传问题时，可以通过日志记录进行排查
3. **数据统计**: 可以统计文件上传的数量和成功率
4. **重复上传检查**: 可以通过文件路径或文件名检查是否已经上传过相同文件

## 注意事项

1. 文件日志记录是在文件上传成功后进行的，如果上传失败则不会记录
2. 日志记录失败不会影响主要业务流程
3. `applySerialNo`参数实际上就是`creditId`，在HXBK业务中两者是相同的
4. 每个文件（包括ZIP文件和索引文件）都会单独记录一条日志

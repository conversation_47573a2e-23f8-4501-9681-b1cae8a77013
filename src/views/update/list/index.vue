<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="客户端">
        <el-select
          v-model="queryParams.clientNum"
          placeholder="请选择"
          style="width: 200px;"
          clearable
          @change="handleQuery"
        >
          <el-option label="安卓" value="android" />
          <el-option label="ios" value="ios" />
          <el-option label="小程序" value="mini" />
        </el-select>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 16px;">
      <el-dropdown @command="handleCommand">
        <el-button round type="primary" icon="el-icon-plus" size="mini">新建版本</el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="android">安卓</el-dropdown-item>
          <el-dropdown-item command="ios">ios</el-dropdown-item>
          <el-dropdown-item command="mini">小程序</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>

    <!-- 列表 -->
    <el-table v-loading="loading" border="border" :data="list">
      <el-table-column label="客户端" prop="clientNum" align="center" />
      <el-table-column label="版本名称" prop="versionName" align="center" />
      <el-table-column label="版本号" prop="versionNum" align="center" />
      <el-table-column label="升级类型" prop="pushType" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.pushType === 'Y'">强制</span>
          <span v-if="scope.row.pushType === 'N'">非强制</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" prop="createdTime" align="center" />
      <el-table-column label="更新时间" prop="updatedTime" align="center" />
      <el-table-column label="包渠道" prop="" align="center">
        <template slot-scope="scope">
          <span v-for="item in scope.row.appChannelConfigs" :key="item.channelNum">{{ item.channelName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.pushEnableFlag"
            active-color="#1a7efd"
            inactive-color="#ccc"
            @change="(e) => changeSwitch(e, scope.row)"
          />
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template slot-scope="scope">
          <el-button type="text" @click="hanleEdite(scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 新增 -->
    <el-dialog
      :title="`app更新-${ruleForm.clientNum === 'ios' ? 'ios' : ruleForm.clientNum === 'mini'? '小程序' : '安卓'}`"
      :visible.sync="visible1"
      width="500px"
      :before-close="reset1"
    >
      <el-form ref="ruleForm" label-position="right" label-width="100px" :model="ruleForm" :rules="rules">
        <el-form-item label="版本名称" prop="versionName">
          <el-input v-model="ruleForm.versionName" />
        </el-form-item>
        <el-form-item label="版本号" prop="versionNum">
          <el-input v-model="ruleForm.versionNum" />
        </el-form-item>
        <el-form-item label="是否强制更新" prop="pushType">
          <el-radio-group v-model="ruleForm.pushType">
            <el-radio label="Y">强制</el-radio>
            <el-radio label="N">非强制</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.clientNum === 'android'" label="安卓包渠道" prop="channelList">
          <el-checkbox-group v-model="ruleForm.channelList">
            <el-checkbox v-for="item in channelListOptions" :key="item.channelNum" :label="item.channelNum" name="channelList">{{ item.channelName
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item v-if="ruleForm.clientNum === 'mini'" label="小程序渠道" prop="channelList">
          <el-checkbox-group v-model="ruleForm.channelList">
            <el-checkbox v-for="item in channelListOptions" :key="item.channelNum" :label="item.channelNum" name="channelList">{{ item.channelName
            }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="app包链接" prop="pushUrl">
          <el-input v-model="ruleForm.pushUrl" />
        </el-form-item>
        <el-form-item label="更新内容" prop="pushContext">
          <el-input v-model="ruleForm.pushContext" type="textarea" />
          <div style="font-size: 13px; color: #888;">最多3行,30字</div>
        </el-form-item>
        <el-form-item>
          <el-button round type="primary" @click="submitForm('ruleForm')">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  query,
  add,
  channelsQuery,
  update
} from '@/api/app'

export default {
  name: '',
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        clientNum: undefined
      },
      loading: false,
      list: [],
      total: 0,

      visible1: false,
      channelListOptions: [],
      ruleForm: {
        channelList: []
      },
      rules: {
        versionName: [
          { required: true, message: '请输入版本名称', trigger: 'blur' }
        ],
        versionNum: [
          { required: true, message: '请输入版本号', trigger: 'blur' }
        ],
        pushType: [
          { required: true, message: '请选择是否强制更新', trigger: 'blur' }
        ],
        pushContext: [
          { required: true, message: '请输入更新内容', trigger: 'blur' }
        ],
        pushUrl: [
          { required: true, message: 'app包链接', trigger: 'blur' }
        ]
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 新增
    handleCommand(command) {
      this.ruleForm.clientNum = command

      channelsQuery({
        pageNum: 1,
        pageSize: 1000
      }).then(res => {
        const arr = res.data.list.filter(item => item.clientNum === command && item.clientNum !== 'ios')
        this.channelListOptions = arr

        if (command === 'ios') {
          const ids = []
          res.data.list.forEach(item => {
            if (item.appChannelId && item.appChannelId.startsWith('AC')) {
              ids.push(item.channelNum)
            }
          })
          this.ruleForm.channelList = ids
        }

        this.visible1 = true
      })
    },

    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    // 获取列表
    getList() {
      this.loading = true
      query(this.queryParams).then(res => {
        res.data.list.forEach(item => {
          if (item.pushEnable === 'ENABLE') {
            item.pushEnableFlag = true
          } else {
            item.pushEnableFlag = false
          }
        })
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },

    // 切换状态
    changeSwitch(e, row) {
      const params = {
        ...row,
        pushEnable: e ? 'ENABLE' : 'DISABLE',
        channelList: row.appChannelConfigs.map(item => item.channelNum)
      }
      update(params).then(res => {
        this.getList()
      })
    },

    // 提交新增，编辑
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            ...this.ruleForm,
            channelList: this.ruleForm.clientNum === 'ios' ? ['ios'] : this.ruleForm.channelList
            // pushType: this.ruleForm.clientNum === 'android' ? 'Y' : this.ruleForm.pushType,
          }

          if (this.ruleForm.clientNumId) {
            update(params).then(res => {
              this.reset1()
              this.getList()
            })
          } else {
            add({ ...params, pushEnable: 'ENABLE' }).then(res => {
              this.reset1()
              this.getList()
            })
          }
        }
      })
    },

    reset1() {
      this.visible1 = false
      this.$refs['ruleForm'].resetFields()
      this.ruleForm = { channelList: [] }
    },

    // 编辑
    hanleEdite(row) {
      this.ruleForm = {
        ...row,
        channelList: row.appChannelConfigs.map(item => item.channelNum)
      }

      if (row.clientNum === 'android' || row.clientNum === 'mini') {
        channelsQuery({
          pageNum: 1,
          pageSize: 1000
        }).then(res => {
          const arr = res.data.list.filter(item => item.clientNum === row.clientNum)
          this.channelListOptions = arr
          this.visible1 = true
        })
      } else {
        this.visible1 = true
      }
    }

  }
}
</script>

<style scoped>
.list {
  margin-bottom: 16px;
}

.list .item {
  display: flex;
  align-items: center;
}

.list .item span {
  width: 150px;
}
</style>

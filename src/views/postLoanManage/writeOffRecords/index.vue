<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item>
        <el-select v-model="queryParams.type" placeholder="请选择" style="width: 100px;">
          <el-option label="手机号" value="mobile"></el-option>
          <el-option label="订单编号" value="orderId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-input placeholder="请输入" clearable v-model="queryParams.typeText" style="width:200px;" />
      </el-form-item>
      <el-form-item>
        <el-select v-model="queryParams.applyState" placeholder="还款状态" style="width: 160px;" clearable>
          <el-option label="还款中" value="PROCESSING"></el-option>
          <el-option label="还款成功" value="SUCCEED"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button round type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
      </el-form-item>
    </el-form>

    <!-- 列表 -->
    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="订单号" align="center" fixed width="280">
        <template slot-scope="scope">
          <el-link :underline="false" type="primary" @click="getPlan(scope.row)">{{ scope.row.orderId }}</el-link>
        </template>
      </el-table-column>
      <el-table-column label="还款状态" prop="applyState" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.applyState === 'INIT'">初始化</span>
          <span v-if="scope.row.applyState === 'PROCESSING'">还款中</span>
          <span v-if="scope.row.applyState === 'FAILED'">还款失败</span>
          <span v-if="scope.row.applyState === 'SUCCEED'">还款成功</span>
        </template>
      </el-table-column>
      <el-table-column label="还款类型" prop="repayPurpose" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.repayPurpose === 'CLEAR'">提前结清</span>
          <span v-if="scope.row.repayPurpose === 'CURRENT'">还当期</span>
        </template>
      </el-table-column>
      <el-table-column label="实还金额" prop="actAmount" align="center"></el-table-column>
      <el-table-column label="应还金额" prop="amount" align="center"></el-table-column>
      <el-table-column label="减免金额" prop="reduceAmount" align="center"></el-table-column>
      <el-table-column label="溢出金额" prop="overflowAmount" align="center"></el-table-column>
      <el-table-column label="操作人" prop="updatedBy" align="center"></el-table-column>
      <el-table-column label="创建时间" prop="createdTime" align="center"></el-table-column>
      <el-table-column label="更新时间" prop="updatedTime" align="center"></el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 还款计划 -->
    <el-dialog title="还款计划" :visible.sync="visible" width="1200px">
      <div v-if="detail">
        <el-table border="border" :data="detail.repayPlanList">
          <el-table-column label="期数" prop="period" align="center"></el-table-column>
          <el-table-column label="应还时间" prop="planRepayDate" align="center"></el-table-column>
          <el-table-column label="实还时间" prop="actRepayTime" align="center"></el-table-column>
          <el-table-column label="当期状态" prop="nowRepayState" align="center"></el-table-column>
          <el-table-column label="还款状态" prop="custRepayState" align="center"></el-table-column>
          <el-table-column label="应还本金" prop="principalAmt" align="center"></el-table-column>
          <el-table-column label="应还利息" prop="interestAmt" align="center"></el-table-column>
          <el-table-column label="应还罚息" prop="penaltyAmt" align="center"></el-table-column>
          <el-table-column label="应还融担费" prop="guaranteeAmt" align="center"></el-table-column>
          <el-table-column label="应还服务费" prop="consultFee" align="center"></el-table-column>
          <el-table-column label="应还违约金" prop="breachFee" align="center"></el-table-column>
          <el-table-column label="实还本金" prop="actPrincipalAmt" align="center"></el-table-column>
          <el-table-column label="实还利息" prop="actInterestAmt" align="center"></el-table-column>
          <el-table-column label="实还罚息" prop="actPenaltyAmt" align="center"></el-table-column>
          <el-table-column label="实还融担费" prop="actGuaranteeAmt" align="center"></el-table-column>
          <el-table-column label="实还服务费" prop="actConsultFee" align="center"></el-table-column>
          <el-table-column label="实还违约金" prop="actBreachFee" align="center"></el-table-column>
          <el-table-column label="减免金额" prop="reduceAmount" align="center"></el-table-column>
        </el-table>
      </div>
    </el-dialog>

  </div>
</template>

<script>
import { offlineRepayApplySearchPage, queryRepayPlan } from "@/api/postLoanManage";

export default {
  name: "",
  data() {
    return {
      // 查询参数
      queryParams: {
        type: "mobile",
        typeText: "",
        pageNum: 1,
        pageSize: 10,
        applyState: undefined,
      },
      loading: false,
      list: [],
      total: 0,

      visible: false,
      detail:undefined,
    };
  },
  created() {
    this.getList()
  },
  methods: {
    // 搜索
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    // 获取订单列表
    getList() {
      this.loading = true;

      let params = {
        mobile:
          this.queryParams.type === "mobile"
            ? this.queryParams.typeText
            : undefined,
        orderId:
          this.queryParams.type === "orderId"
            ? this.queryParams.typeText
            : undefined,
        certNo:
          this.queryParams.type === "certNo"
            ? this.queryParams.typeText
            : undefined,
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        applyState: this.queryParams.applyState,
      };

      offlineRepayApplySearchPage(params).then(res => {
        this.list = res.data.list;
        this.total = res.data.total;
        this.loading = false;
      });
    },

    // 还款计划
    getPlan(row){
      queryRepayPlan({orderId: row.orderId}).then(res => {
        this.detail = res.data
        this.visible = true
      });
    }
  }
};
</script>
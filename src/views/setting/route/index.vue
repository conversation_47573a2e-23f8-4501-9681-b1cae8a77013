<template>
  <div class="app-container">
    <el-button
      v-permission="['admin','route:add']"
      round
      type="primary"
      size="mini"
      @click="add"
      style="margin-bottom: 16px;"
      >新增</el-button
    >
    <div v-if="!loading && list && list.length === 0" class="empty">
      还没有任何配置
    </div>
    <template v-if="list && list.length > 0">
      <el-table border="border" :data="list" v-loading="loading">
        <el-table-column label="渠道方" prop="flowChannel" align="left" width="180px">
          <template slot-scope="scope">
            <span
              :style="{
                opacity: scope.row.flowEnabled === 'DISABLE' ? '0.3' : 1
              }"
              >{{ scope.row.desc }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="渠道编码" prop="flowChannel" align="left" width="180px">
          <template slot-scope="scope">
            <span
              :style="{
                opacity: scope.row.flowEnabled === 'DISABLE' ? '0.3' : 1
              }"
              >{{ scope.row.flowChannel }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="资方优先级"
          align="left"
          min-width="300px"
        >
          <!-- scope.row.capitalConfigStr -->
          <template slot-scope="scope">

            <span v-for="(flow, index) in scope.row.capitalConfigList" :key="flow.capitalCode"
              :style="{
                // opacity: scope.row.flowEnabled === 'DISABLE' ? '0.3' : 1
                opacity: flow.enabled === 'DISABLE' ? '0.3' : 1
              }"
              >{{ flow.capitalConfigStr }};</span
            >
          </template>
        </el-table-column>
        <el-table-column label="修改时间" prop="updatedTime" align="left" width="200px">
          <template slot-scope="scope">
            <span
              :style="{
                opacity: scope.row.flowEnabled === 'DISABLE' ? '0.3' : 1
              }"
              >{{ scope.row.updatedTime }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="修改人" prop="updatedBy" align="left" width="200px">
          <template slot-scope="scope">
            <span
              :style="{
                opacity: scope.row.flowEnabled === 'DISABLE' ? '0.3' : 1
              }"
              >{{ scope.row.updatedBy }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="200px">
          <template slot-scope="scope">
            <el-button
              v-permission="['admin','route:view']"
              style="padding: 0;"
              type="text"
              size="mini"
              @click="detail(scope.row.id)"
              >查看</el-button
            >
            <el-button
              v-permission="['admin','route:edite']"
              style="padding: 0;"
              type="text"
              size="mini"
              @click="edite(scope.row.id)"
              >修改</el-button
            >
            <el-button
              v-permission="['admin','route:open']"
              style="padding: 0;"
              v-if="scope.row.flowEnabled === 'DISABLE'"
              type="text"
              size="mini"
              @click="toggle(scope.row.id, 'ENABLE')"
              >启用</el-button
            >
            <el-button
              v-permission="['admin','route:stop']"
              style="padding: 0;color:#c00"
              v-if="scope.row.flowEnabled === 'ENABLE'"
              type="text"
              size="mini"
              @click="toggle(scope.row.id, 'DISABLE')"
              >停用</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!-- <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" /> -->
    </template>

    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="500px"
      :before-close="cancel"
    >
      <div class="box">
        <span class="label">渠道方</span>
        <el-select
          :disabled="mode === 'detail' || mode === 'edite'"
          v-model="flowId"
          placeholder="下拉选择渠道"
          style="flex: 1;"
          @change="change"
          clearable
        >
          <el-option
            v-for="item in options"
            :label="item.desc"
            :value="item.id"
            :key="item.id"
          ></el-option>
        </el-select>
      </div>
      <div
        v-if="errorShow"
        style="color:#c00; margin-left: 60px; font-size: 12px; margin-bottom: 16px; margin-top: -10px;"
      >
        渠道方已存在
      </div>
      <div v-if="mode !== 'detail'" class="tip">
        鼠标拖拽或点击[↑]或[↓]可移动顺序
      </div>
      <div class="table-head">
        <span>优先级</span>
        <span>资方名称</span>
        <span v-if="mode !== 'detail'">操作</span>
      </div>
      <div class="table-body">
        <template v-if="mode === 'detail'">
          <div class="item" v-for="(item, index) in capitalListSeled">
            <span
              :style="{ opacity: item.enabled === 'DISABLE' ? '0.3' : 1 }"
              >{{ index + 1 }}</span
            >
            <span :style="{ opacity: item.enabled === 'DISABLE' ? '0.3' : 1 }">
              {{ item.desc }}
              <i class="dis-tip" v-if="item.capitalEnabled === 'DISABLE'"
                >资方已停用</i
              >
            </span>
          </div>
        </template>
        <template v-else>
          <draggable
            v-model="capitalListSeled"
            @change="changeDrag"
            @start="drag = true"
            @end="drag = false"
          >
            <div class="item" v-for="(item, index) in capitalListSeled">
              <span
                :style="{ opacity: item.enabled === 'DISABLE' ? '0.3' : 1 }"
                >{{ index + 1 }}</span
              >
              <span :style="{ opacity: item.enabled === 'DISABLE' ? '0.3' : 1 }"
                >{{ item.desc
                }}<i class="dis-tip" v-if="item.capitalEnabled === 'DISABLE'"
                  >资方已停用</i
                ></span
              >
              <div class="item-btns">
                <i
                  class="el-icon-top"
                  style="margin-right: 10px;"
                  :style="{
                    color: index === 0 ? '#e5e5e5' : '#1a7efd',
                    cursor: index === 0 ? 'not-allowed' : 'pointer'
                  }"
                  @click="up(index)"
                ></i>
                <i
                  class="el-icon-bottom"
                  style="margin-right: 10px;"
                  :style="{
                    color:
                      index + 1 === capitalListSeled.length
                        ? '#e5e5e5'
                        : '#1a7efd',
                    cursor:
                      index + 1 === capitalListSeled.length
                        ? 'not-allowed'
                        : 'pointer'
                  }"
                  @click="down(index)"
                ></i>
                <el-button
                  v-if="item.enabled === 'ENABLE'"
                  type="text"
                  style="color: #c00; padding: 0;"
                  @click="stop(item)"
                  >停用</el-button
                >
                <el-button
                  v-if="item.enabled === 'DISABLE'"
                  type="text"
                  style="padding: 0;"
                  @click="open(item)"
                  >启用</el-button
                >
                <el-button
                  v-if="item.enabled === 'DISABLE'"
                  type="text"
                  style="color: #c00; padding: 0;"
                  @click="del(item)"
                  >删除</el-button
                >
              </div>
            </div>
          </draggable>
        </template>
      </div>
      <div class="new-btn" v-if="mode !== 'detail'">
        <el-button type="text" size="mini" @click="addItem">新增资方</el-button>
        <div class="drop" v-if="dropVisible">
          <div
            class="drop-list"
            v-for="(item, index) in capitalList"
            :key="item.name"
          >
            <el-checkbox v-model="item.selected" :disabled="item.disabled">{{
              item.desc
            }}</el-checkbox>
          </div>
          <div class="btn-box">
            <el-button
              type="text"
              style="padding: 5px;"
              size="mini"
              @click="addItemOk"
              >确定</el-button
            >
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer" v-if="mode !== 'detail'">
        <el-button round @click="cancel">关闭</el-button>
        <el-button round type="primary" @click="save">保存</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFlowConfig,
  updateFlowRouteConfig,
  getFlowRouteConfigInfo,
  enableFlowConfig,
  getCapitalList,
  queryFlowRouteConfigPage
} from "@/api/setting";
import draggable from "vuedraggable";

export default {
  name: "",
  data() {
    return {
      queryParams: {
        pageNum: 1,
        pageSize: 1000
      },
      total: 0,
      options: [],

      loading: true,
      list: undefined,

      mode: "",
      dialogVisible: false,
      title: "",

      flowId: undefined,

      dropVisible: false,
      capitalList: [],
      capitalListSeled: [],
      capitalListDel: [],
      errorShow: false
    };
  },
  created() {
    // 获取所有渠道方
    getFlowConfig().then(res => {
      this.options = res.data;
    });

    this.getList();
  },
  methods: {
    // 获取列表
    getList() {
      this.loading = true;
      queryFlowRouteConfigPage(this.queryParams).then(res => {
        this.list = res.data.list.map(item => {
          if (!item.capitalConfigList) item.capitalConfigList = []
          return item
        })
        // this.total = res.data.total
        this.loading = false;
      });
    },

    // 新增弹窗
    add() {
      this.title = "新增";
      this.mode = "add";
      this.dialogVisible = true;
    },

    // 关闭新增、编辑弹窗
    cancel() {
      this.dialogVisible = false;

      this.title = "";
      this.flowId = undefined;
      this.capitalList = [];
      this.capitalListSeled = [];
      this.dropVisible = false;
    },

    // 渠道方change
    change(e) {
      const obj = this.list.find(item => item.id === e);
      if (obj && this.mode === "add") {
        this.errorShow = true;
      } else {
        this.errorShow = false;
      }
    },

    changeDrag(e) {
      if (e.added) {
        let userId = e.added.element.userId;
        let inBoundUsers = this.info.boundUsers.find(
          item => item.userId === userId
        );

        let deptId;
        if (inBoundUsers) {
          deptId = this.info.deptId;
        } else {
          deptId = this.unknownDeptId;
        }

        let params = {
          userId: e.added.element.userId,
          deptId
        };
        userBoundDept(params).then(res => {
          this.getList();
        });
      }
    },

    up(index) {
      if (index === 0) {
        return;
      }
      [this.capitalListSeled[index], this.capitalListSeled[index - 1]] = [
        this.capitalListSeled[index - 1],
        this.capitalListSeled[index]
      ];
      this.$forceUpdate();
    },

    down(index) {
      if (index + 1 === this.capitalListSeled.length) {
        return;
      }
      [this.capitalListSeled[index], this.capitalListSeled[index + 1]] = [
        this.capitalListSeled[index + 1],
        this.capitalListSeled[index]
      ];
      this.$forceUpdate();
    },

    // 新增/编辑 保存
    save() {
      if (!this.flowId) {
        this.$message.error("请选择渠道方！");
        return;
      }
      if (this.capitalListSeled.length === 0) {
        this.$message.error("请添加资方！");
        return;
      }

      let list;
      if (this.mode === "add") {
        list = this.capitalListSeled.map((item, index) => {
          return {
            capitalId: item.id,
            enabled: item.enabled, // DISABLE: 停用 ENABLE: 启用
            valid: "Y", // Y: 有效/正常 N: 被删除
            priority: index + 1
          };
        });
      } else if (this.mode === "edite") {
        list = [...this.capitalListSeled, ...this.capitalListDel];

        list = list.map((item, index) => {
          return {
            id: item.id,
            capitalId: item.capitalId,
            enabled: item.enabled, // DISABLE: 停用 ENABLE: 启用
            valid: item.valid ? item.valid : "Y", // Y: 有效/正常 N: 被删除
            priority: index + 1
          };
        });
      }

      let params = {
        flowId: this.flowId,
        list
      };

      updateFlowRouteConfig(params).then(res => {
        this.cancel();
        this.getList();
      });
    },

    // 新增资方弹窗
    addItem() {
      if (this.mode === "add") {
        getCapitalList().then(res => {
          res.data.forEach(item => {
            item.enabled = "ENABLE";
            const obj = this.capitalListSeled.find(x => x.name === item.name);
            if (obj) {
              item.selected = true;
              item.disabled = true;
            } else {
              item.selected = false;
              item.disabled = false;
            }
          });
          this.capitalList = res.data;
          this.dropVisible = true;
        });
      } else if (this.mode === "edite") {
        this.dropVisible = true;
      }
    },

    // 新增资方弹窗确定
    addItemOk() {
      let arr = this.capitalList.filter(
        item => item.selected && !item.disabled
      );
      this.capitalListSeled = [...this.capitalListSeled, ...arr];
      this.dropVisible = false;

      arr.forEach((item, index) => {
        this.capitalListDel.forEach((x, index1) => {
          if (item.name === x.name) {
            this.capitalListDel.splice(index1, 1);
          }
        });
      });

      this.capitalList.forEach(item => {
        item.disabled = item.selected;
      });
    },

    // 删除资方
    del(obj) {
      this.$confirm(`是否确认删除该资方?`, "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        const x = this.capitalList.findIndex(item => item.name === obj.name);
        this.capitalList[x].selected = false;
        this.capitalList[x].disabled = false;

        const y = this.capitalListSeled.findIndex(
          item => item.name === obj.name
        );
        this.capitalListSeled.splice(y, 1);

        if (obj.id) {
          this.capitalListDel = [
            ...this.capitalListDel,
            { ...obj, valid: "N" }
          ];
        }
      });
    },

    // 停用
    stop(obj) {
      const y = this.capitalListSeled.findIndex(item => item.name === obj.name);
      this.capitalListSeled[y].enabled = "DISABLE";
    },

    open(obj) {
      const y = this.capitalListSeled.findIndex(item => item.name === obj.name);
      this.capitalListSeled[y].enabled = "ENABLE";
    },

    // 渠道停用，启用
    toggle(id, type) {
      this.$confirm(
        `是否确认 ${type === "ENABLE" ? "启用" : "停用"} 该渠道方?`,
        "警告",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          roundButton: true,
          type: "warning"
        }
      ).then(() => {
        enableFlowConfig({ flowConfigId: id, enabled: type }).then(res => {
          this.getList();
        });
      });
    },

    // 查看
    detail(id) {
      getFlowRouteConfigInfo({ flowId: id }).then(res => {
        this.mode = "detail";
        this.title = "查看";
        this.flowId = res.data.flowId;
        this.capitalListSeled = res.data.selectedList;
        this.dialogVisible = true;
      });
    },

    edite(id) {
      getFlowRouteConfigInfo({ flowId: id }).then(res => {
        this.mode = "edite";
        this.title = "修改";
        this.flowId = res.data.flowId;

        let arr = res.data.selectedList.map(item => {
          return {
            ...item,
            name: item.bankChannel
          };
        });
        this.capitalListSeled = arr;

        let arr1 = res.data.availableList.map(item => {
          const obj = res.data.selectedList.find(
            x => x.bankChannel === item.bankChannel
          );

          return {
            ...item,
            name: item.bankChannel,
            disabled: obj ? true : false,
            selected: obj ? true : false
          };
        });
        this.capitalList = arr1;

        this.dialogVisible = true;
      });
    }
  },
  components: {
    draggable
  }
};
</script>

<style scoped>
.empty {
  font-size: 30px;
  color: #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 500px;
}

.box {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.box .label {
  margin-right: 16px;
}

.tip {
  font-size: 12px;
  color: #999;
  margin-bottom: 16px;
}

.table-head {
  background: #f5f5f5;
  display: flex;
  align-items: center;
}

.table-head span {
  padding: 10px;
  font-size: 13px;
}

.table-head span:nth-child(1) {
  width: 20%;
}

.table-head span:nth-child(2) {
  width: 50%;
}

.table-head span:nth-child(3) {
  flex: 1;
}

.table-body .item {
  display: flex;
  align-items: center;
  border-bottom: #e5e5e5 1px solid;
  background: #fff;
}

.table-body .item:hover {
  background: #e5e5e5;
}

.table-body .item span:nth-child(1) {
  width: 20%;
  padding: 10px;
}

.table-body .item span:nth-child(2) {
  width: 50%;
  padding: 10px;
}

.item-btns {
  padding: 5px 10px;
  flex: 1;
  display: flex;
  align-items: center;
}

.new-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 5px;
  border-bottom: #e5e5e5 1px solid;
  position: relative;
}

.drop {
  background: #fff;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  position: absolute;
  left: 240px;
  top: 30px;
  padding: 6px 16px;
  border-radius: 10px;
  width: 180px;
}

.drop-list {
  padding: 5px 0;
}

.btn-box {
  display: flex;
  justify-content: flex-end;
  padding: 6px 0 6px 0;
}

.dis-tip {
  font-style: normal;
  font-size: 12px;
  background: #f5f5f5;
  display: inline-block;
  padding: 4px 4px;
  border-radius: 4px;
  border: #e5e5e5 1px solid;
  line-height: 1;
  margin-left: 8px;
}
</style>

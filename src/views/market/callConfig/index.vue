<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="策略编号" prop="" width="150px">
        <el-input placeholder="请输入" clearable size="small" />
      </el-form-item>
      <el-form-item label="策略名称" prop="" width="150px">
        <el-input placeholder="请输入" clearable size="small" />
      </el-form-item>
      <el-form-item label="话术ID" prop="" width="150px">
        <el-input placeholder="请输入" clearable size="small" />
      </el-form-item>
      <el-form-item label="策略状态" prop="">
        <el-select placeholder="请选择" style="width: 110px" clearable>
          <!-- <el-option v-for="item in repayStateOptions" :label="item.label" :value="item.value" :key="item.value"></el-option> -->
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" round icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button round size="mini" @click="handleQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 10px;">
      <el-button type="success" round size="mini">添加</el-button>
      <el-button type="success" round size="mini">导入发送</el-button>
    </div>

    <el-table border="border" :data="list" v-loading="loading">
      <el-table-column label="操作" align="center" fixed>
        <template slot-scope="scope">
          <el-button type="primary" icon="el-icon-edit" circle></el-button>
          <el-switch v-model="scope.row.checked" active-color="#1a7efd" inactive-color="#cccccc">
          </el-switch>
        </template>
      </el-table-column>
      <el-table-column label="策略编号" prop="" align="center"></el-table-column>
      <el-table-column label="策略名称" prop="" align="center"></el-table-column>
      <el-table-column label="合作方" prop="" align="center"></el-table-column>
      <el-table-column label="话术ID" prop="" align="center"></el-table-column>
      <el-table-column label="推送时间" prop="" align="center"></el-table-column>
      <el-table-column label="策略状态" prop="" align="center"></el-table-column>
      <el-table-column label="修改时间" prop="" align="center"></el-table-column>
      <el-table-column label="修改人" prop="" align="center"></el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
// import { rightsQuery, rightsDownload } from "@/api/rights";
import { get as getDictByName } from "@/api/system/dictDetail"

export default {
  name: "",
  data() {
    return {
      loading: false,
      total: 0,
      list: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
      },
      dataVal: [],
      pickerOptionsDefault: {
        onPick: ({ maxDate, minDate }) => {
          //当我们选择两个值的时候，就认为用户已经选择完毕
          if (maxDate != null && minDate != null) {
            this.repayDateBegin = maxDate;
            this.repayDateEnd = minDate;
          }
        },
        disabledDate: (time) => {
          let maxDate = this.maxDate;
          let minDate = this.minDate;
          if (maxDate != null && minDate != null) {
            let days = maxDate.getTime() - minDate.getTime();
            //计算完之后必须清除，否则选择器一直处于禁止选择的状态
            this.maxDate = null;
            this.minDate = null;
            return parseInt(days / (1000 * 60 * 60 * 24)) > 30;
          } else {
            //设置当前时间后的时间不可选
            return time.getTime() > Date.now();
          }
        },
      },
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      // this.loading = true;
      // if (this.dataVal && this.dataVal.length === 2) {
      //   this.queryParams.startDate = this.dataVal[0]
      //   this.queryParams.endDate = this.dataVal[1]
      // }
      // rightsQuery(this.queryParams).then((res) => {
      //   this.list = res.data.list;
      //   this.total = res.data.total;
      //   this.loading = false;
      // });
    },
  },
};
</script>

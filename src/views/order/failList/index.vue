<template>
  <div class="app-container">
    <el-form ref="queryForm" :model="queryParams" :inline="true">
      <el-form-item label="流量方" prop="flowChannel" width="150px">
        <el-select v-model="queryParams.flowChannel" placeholder="流量方" style="width: 140px" clearable>
          <el-option v-for="item in flowChannelOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="资方" prop="bankChannel" width="150px">
        <el-select placeholder="资金方" v-model="queryParams.bankChannel" style="width: 140px;" clearable>
          <el-option v-for="item in bankChannelOptions" :label="item.label" :value="item.value"
            :key="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="时间" prop="dataVal">
        <el-date-picker v-model="dataVal" size="small" type="datetimerange" style="width: 370px"
          value-format="yyyy-MM-dd HH:mm:ss" range-separator="-" start-placeholder="开始时间" end-placeholder="结束时间"
          :default-time="['00:00:00', '23:59:59']" clearable></el-date-picker>
      </el-form-item>
      <el-form-item label="手机号" prop="mobile" width="150px">
        <el-input v-model="queryParams.mobile" placeholder="输入手机号" clearable size="small" />
      </el-form-item>
      <el-form-item label="订单编号" prop="orderId" width="150px">
        <el-input v-model="queryParams.orderId" placeholder="输入订单编号" clearable size="small" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" round icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
      </el-form-item>
    </el-form>

    <div style="margin-bottom: 16px;">
      <el-button type="primary" plain round size="mini" @click="handleCloseSel">批量关闭订单</el-button>
      <el-button type="primary" plain round size="mini" @click="handleRouterSel">路由到下个资方</el-button>
      <el-button type="primary" plain round size="mini" @click="handleReapply">原资方重推放款</el-button>
    </div>

    <el-table v-loading="loading" border="border" :data="list" ref="multipleTable"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55">
      </el-table-column>
      <el-table-column label="流量方订单编号" prop="orderId" align="center" />
      <el-table-column label="借据号" prop="loanId" align="center" />
      <el-table-column label="手机号" prop="mobile" align="center" />
      <el-table-column label="身份证号" prop="certNo" align="center" />
      <el-table-column label="流量方" prop="flowChannel" align="center" />
      <el-table-column label="资金方" prop="bankChannel" align="center" />
      <el-table-column label="失败原因" prop="failReason" align="center" />
      <el-table-column label="订单处理" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="handleClose(scope.row)">关闭订单</el-button>
          <el-button type="text" @click="handleRouter(scope.row)">路由到下个资方</el-button>
          <el-button type="text" @click="singleReapply(scope.row)">原资方重推放款</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />
  </div>
</template>

<script>
import { flowChannelQuery, bankChannelQuery, preFailQuery, preFail, loanRoute, loanReapply } from '@/api/order'

export default {
  name: 'FailList',
  data() {
    return {
      flowChannelOptions: [],
      bankChannelOptions: [],

      loading: false,
      total: 0,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10
      },
      list: [],

      dataVal: [],
      multipleSelection: []
    }
  },
  created() {
    flowChannelQuery().then(res => {
      let arr = []
      for (let key in res.data) {
        arr.push({
          label: res.data[key],
          value: key
        })
      }
      this.flowChannelOptions = arr
    });
    bankChannelQuery().then(res => {
      let arr = []
      for (let key in res.data) {
        arr.push({
          label: res.data[key],
          value: key
        })
      }
      this.bankChannelOptions = arr;
    });

    this.getList()
  },
  methods: {
    /** 查询参数列表 */
    getList() {
      this.loading = true
      if (this.dataVal && this.dataVal.length === 2) {
        this.queryParams.startTime = this.dataVal[0]
        this.queryParams.endTime = this.dataVal[1]
      } else {
        this.queryParams.startTime = undefined
        this.queryParams.endTime = undefined
      }

      preFailQuery(this.queryParams).then((res) => {
        this.list = res.data.list
        this.total = res.data.total
        this.loading = false
      })
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },

    flowChannelFormat({ flowChannel }) {
      const obj = this.flowChannelOptions.find(item => item.value === flowChannel)
      return obj ? obj.label : '--'
    },

    bankChannelFormat({ bankChannel }) {
      const obj = this.bankChannelOptions.find(item => item.value === bankChannel)
      return obj ? obj.label : '--'
    },

    handleSelectionChange(val) {
      this.multipleSelection = val;
    },


    // 批量关闭
    handleCloseSel() {
      if (this.multipleSelection && this.multipleSelection.length) {
        this.$confirm('是否确认批量关闭订单?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          roundButton: true,
          type: "warning"
        }).then(() => {
          let loanIds = this.multipleSelection.map(item => item.loanId)
          preFail(loanIds).then(() => {
            this.$message.success('操作成功')
            this.getList()
          })
        })
      }
    },
    // 单个关闭
    handleClose(row) {
      this.$confirm('是否确认关闭订单?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        let params = [row.loanId]
        preFail(params).then(() => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    },

    // 批量路由到下个资方
    handleRouterSel() {
      if (this.multipleSelection && this.multipleSelection.length) {
        this.$confirm('是否确认批量路由到下个资方?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          roundButton: true,
          type: "warning"
        }).then(() => {
          let loanIds = this.multipleSelection.map(item => item.loanId)
          loanRoute(loanIds).then(() => {
            this.$message.success('操作成功')
            this.getList()
          })
        })
      }
    },

    // 单个路由到下个资方
    handleRouter(row) {
      this.$confirm('是否确认路由到下个资方?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        let params = [row.loanId]
        loanRoute(params).then(() => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    },


    // 批量原资方重推放款
    handleReapply() {
      if (this.multipleSelection && this.multipleSelection.length) {
        this.$confirm('是否确认批量原资方重推放款?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          roundButton: true,
          type: "warning"
        }).then(() => {
          let loanIds = this.multipleSelection.map(item => item.loanId)
          loanReapply(loanIds).then(() => {
            this.$message.success('操作成功')
            this.getList()
          })
        })
      } else {
        this.$message.warning('请至少选择一条数据')
      }
    },
    // 单个原资方重推放款
    singleReapply(row) {
      this.$confirm('是否确认原资方重推放款?', "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        roundButton: true,
        type: "warning"
      }).then(() => {
        let params = [row.loanId]
        loanReapply(params).then(() => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    },

  }
}
</script>

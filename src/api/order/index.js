import request from '@/utils/request'

// 订单查询
export function orderQuery(data) {
  return request({
    url: 'order/query',
    method: 'post',
    data
  })
}

// 客户信息查询
export function orderCustom(data) {
  return request({
    url: 'order/custom',
    method: 'post',
    data
  })
}

// 查看人脸
export function orderFace(data) {
  return request({
    url: 'order/face',
    method: 'post',
    data
  })
}

// 查看进度
export function orderStage(data) {
  return request({
    url: 'order/stage',
    method: 'post',
    data
  })
}

// 查看还款计划
export function orderPlan(data) {
  return request({
    url: 'order/plan',
    method: 'post',
    data
  })
}

// 订单列表
export function orderList(data) {
  return request({
    url: 'order/list',
    method: 'post',
    data
  })
}

// 签章协议
export function agreementlist(data) {
  return request({
    url: 'order/agreementlist',
    method: 'post',
    data
  })
}

export function downloadVoucherFile(data) {
  return request({
    url: '/order/downloadVoucherFile',
    method: 'post',
    data
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/order/resetPassword',
    method: 'post',
    data
  })
}

// 取消
export function cancel(data) {
  return request({
    url: '/order/cancel',
    method: 'post',
    data
  })
}


// 查询流量渠道
export function flowChannelQuery() {
  return request({
    url: '/loan/flowChannel/query',
    method: 'get'
  })
}

// 查询资方渠道
export function bankChannelQuery() {
  return request({
    url: '/loan/bankChannel/query',
    method: 'get'
  })
}

// 查询失败订单
export function preFailQuery(data) {
  return request({
    url: '/loan/preFail/query',
    method: 'post',
    data,
  })
}

// 失败订单置为失败
export function preFail(data) {
  return request({
    url: '/loan/preFail/fail',
    method: 'post',
    data
  })
}

// 路由到下个资方
export function loanRoute(data) {
  return request({
    url: '/loan/route',
    method: 'post',
    data
  })
}
// 原资方重推放款
export function loanReapply(data) {
  return request({
    url: '/loan/reapply',
    method: 'post',
    data
  })
}


// 进件列表
export function preOrderFailList(data) {
  return request({
    url: '/preOrder/failList',
    method: 'post',
    data
  })
}

// 外呼坐席
export function handleCall(data) {
  return request({
    url: '/order/handleCall',
    method: 'post',
    data
  })
}

// 新增外呼备注
export function callSave(data) {
  return request({
    url: '/order/call/save',
    method: 'post',
    data
  })
}
// 新增外呼备注
export function queryCallRecord(data) {
  return request({
    url: '/order/queryCallRecord',
    method: 'post',
    data
  })
}
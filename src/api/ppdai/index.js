import request from '@/utils/request'

// 放款订单信息查询 ppdai 减免/还款 列表
export function queryLoanPage(data) {
  return request({
    url: '/ppd/loan/queryLoanPage',
    method: 'post',
    data
  })
}

// 减免订单列表信息查询
export function queryReducePage(data) {
  return request({
    url: '/ppd/loan/queryReducePage',
    method: 'post',
    data
  })
}

// ppdai 还款计划查询
export function queryRepayPlan(data) {
  return request({
    url: '/ppd/loan/queryRepayPlan',
    method: 'post',
    data
  })
}
// 线下还款计划查询
export function repayOverPlanQuery(data) {
  return request({
    url: '/ppd/loan/repayOverPlanQuery',
    method: 'post',
    data
  })
}

// 减免申请
export function reduceApply(data) {
  return request({
    url: '/ppd/loan/reduceApply',
    method: 'post',
    data
  })
}

// 减免审核
export function reduceAudit(data) {
  return request({
    url: '/ppd/loan/reduceAudit',
    method: 'post',
    data
  })
}


// 线下还款申请
export function offlineRepay(data) {
  return request({
    url: '/ppd/loan/offlineRepay',
    method: 'post',
    data
  })
}
// 结清试算
export function clearTrail(data) {
  return request({
    url: '/ppd/loan/trail',
    method: 'post',
    data
  })
}

// 文件下载
export function downloadFile(data) {
  return request({
    url: '/ppd/file/downloadFile',
    method: 'post',
    data
  })
}

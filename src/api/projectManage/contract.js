import request from '@/utils/request'

// 主列表查询
export function getList(data) {
  return request({
    url: 'api/projectContract/page',
    method: 'get',
    params: data
  })
}

// 合同上传
export function upload(data) {
  return request({
    url: 'api/file/upload',
    method: 'post',
    data
  })
}

// 新增合同
export function addContract(data) {
  return request({
    url: 'api/projectContract/create',
    method: 'post',
    data
  })
}

// 下载合同
export function downContract(data) {
  return request({
    url: 'api/file/getFileUrl',
    method: 'post',
    data
  })
}

// 修改合同
export function updateContract(data) {
  return request({
    url: 'api/projectContract/update',
    method: 'post',
    data
  })
}

// 主列表查询
export function getDtl(data) {
  return request({
    url: 'api/projectContract/info',
    method: 'get',
    params: data
  })
}

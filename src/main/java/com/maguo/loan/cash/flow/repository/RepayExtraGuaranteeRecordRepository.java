package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.RepayExtraGuaranteeRecord;
import com.maguo.loan.cash.flow.enums.FeeType;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * @Classname RepayExtraGuaranteeRecordRepository
 * @Description 融担费还款记录生成
 * @Date 2023/10/24 16:29
 * @Created by gale
 */
public interface RepayExtraGuaranteeRecordRepository extends JpaRepository<RepayExtraGuaranteeRecord, String> {
    List<RepayExtraGuaranteeRecord> findByLoanId(String loanId);
    List<RepayExtraGuaranteeRecord> findByLoanIdAndPeriodAndFeeTypeAndRepayStateIn(
            String loanId, Integer period, FeeType feeType, List<ProcessState> repayStates);

    boolean existsByRepayPlanIdAndRepayStateIn(String repayPlanId, List<ProcessState> repayStates);

    List<RepayExtraGuaranteeRecord> findBySourceRepayId(String sourceRepayId);

    List<RepayExtraGuaranteeRecord> findBySourceRepayIdOrderByCreatedTimeAsc(String sourceRepayId);

    List<RepayExtraGuaranteeRecord> findByRepayPlanId(String repayPlanId);

    RepayExtraGuaranteeRecord findTopBySourceRepayIdOrderByCreatedTimeAsc(String sourceRepayId);

    RepayExtraGuaranteeRecord findTopBySourceRepayIdOrderByCreatedTimeDesc(String sourceRepayId);

    List<RepayExtraGuaranteeRecord> findBySourceRepayIdOrderByCreatedTimeDesc(String sourceRepayId);

    Boolean existsBySourceRepayIdAndRepayState(String crrId, ProcessState repayState);

    RepayExtraGuaranteeRecord findTopBySourceRepayIdAndRepayStateOrderByCreatedTimeDesc(String sourceRepayId, ProcessState repayState);

}

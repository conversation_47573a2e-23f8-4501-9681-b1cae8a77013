package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.FlowRepayRecord;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.RepayRecordType;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface FlowRepayRecordRepository extends JpaRepository<FlowRepayRecord, String> {

    FlowRepayRecord findByPartnerRepayNoAndFlowChannel(String partnerRepayNo, FlowChannel flowChannel);

    Optional<FlowRepayRecord> findByRecordIdAndRepayRecordType(String recordId, RepayRecordType repayRecordType);
}

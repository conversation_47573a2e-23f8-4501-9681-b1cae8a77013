package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.RepayPurpose;
import com.maguo.loan.cash.flow.enums.RepayRecordType;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.time.LocalDateTime;

/**
 * 流量还款记录
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "flow_repay_record")
public class FlowRepayRecord extends BaseEntity {

    /**
     * 流量渠道
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 合作机构还款流水号
     */
    private String partnerRepayNo;

    /**
     * 合作机构单号
     */
    private String partnerOrderNo;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 借据号
     */
    private String loanId;

    /**
     * 期次
     */
    private Integer period;

    /**
     * 还款目的
     */
    @Enumerated(EnumType.STRING)
    private RepayPurpose repayPurpose;

    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    private String recordId;

    @Enumerated(EnumType.STRING)
    private RepayRecordType repayRecordType;


    @Override
    protected String prefix() {
        return "FRR";
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public String getPartnerRepayNo() {
        return partnerRepayNo;
    }

    public void setPartnerRepayNo(String partnerRepayNo) {
        this.partnerRepayNo = partnerRepayNo;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }


    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public RepayRecordType getRepayRecordType() {
        return repayRecordType;
    }

    public void setRepayRecordType(RepayRecordType repayRecordType) {
        this.repayRecordType = repayRecordType;
    }
}

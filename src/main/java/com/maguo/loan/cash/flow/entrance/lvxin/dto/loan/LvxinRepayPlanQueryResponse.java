package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.enums.LvxinApi;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 还款计划查询响应
 * @Date 2024/5/23 14:50
 * @Version v1.0
 **/
public class LvxinRepayPlanQueryResponse implements LvxinRequest {

    private String loanGid;
    private String partnerOrderNo;
    private Integer repaymentStatus;
    @JsonProperty("isOverdue")
    private Boolean isOverdue;
    private Integer stage;
    private List<LvxinRepayPlan> repayPlanList;
    private List<LvxinRightsPlan> privilegeList;

    public String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(String loanGid) {
        this.loanGid = loanGid;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public Integer getRepaymentStatus() {
        return repaymentStatus;
    }

    public void setRepaymentStatus(Integer repaymentStatus) {
        this.repaymentStatus = repaymentStatus;
    }

    public Boolean getIsOverdue() {
        return isOverdue;
    }

    public void setIsOverdue(Boolean overdue) {
        isOverdue = overdue;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }

    public List<LvxinRepayPlan> getRepayPlanList() {
        return repayPlanList;
    }

    public void setRepayPlanList(List<LvxinRepayPlan> repayPlanList) {
        this.repayPlanList = repayPlanList;
    }

    public List<LvxinRightsPlan> getPrivilegeList() {
        return privilegeList;
    }

    public void setPrivilegeList(List<LvxinRightsPlan> privilegeList) {
        this.privilegeList = privilegeList;
    }

    @Override
    public LvxinApi getApiType() {
        return LvxinApi.PUSH_REPAY_PLAN;
    }
}

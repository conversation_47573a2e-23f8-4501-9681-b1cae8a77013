package com.maguo.loan.cash.flow.entrance.lvxin.dto.contract;

/**
 * @ClassName ContractRequest
 * <AUTHOR>
 * @Description 协议列表 请求
 * @Date 2024/3/21 16:23
 * @Version v1.0
 **/
public class ContractRequest {

    /**
     * 协议类型
     * APPLY：申请类合同
     * LOAN：借款类合同
     * RIGHT：权益类合同
     */
    private String type;
    /**
     * 绿信授信流水号
     */
    private String partnerUserId;
    /**
     * 0：未签署；1已签署
     */
    private String signed;
    /**
     * 绿信放款申请流水号
     */
    private String loanGid;
    /**
     * 期数
     */
    private String period;
    /**
     * 借款金额
     */
    private String loanAmount;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public String getSigned() {
        return signed;
    }

    public void setSigned(String signed) {
        this.signed = signed;
    }

    public String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(String loanGid) {
        this.loanGid = loanGid;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(String loanAmount) {
        this.loanAmount = loanAmount;
    }
}

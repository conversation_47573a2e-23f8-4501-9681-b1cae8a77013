package com.maguo.loan.cash.flow.service.listener;


import com.jinghang.common.util.JsonUtil;
import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.dto.CallBackDTO;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.callback.AbstractCallbackService;
import com.maguo.loan.cash.flow.service.callback.CallbackServiceFactory;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * 回调通知监听
 */
@Component
public class CallbackNotifyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CallbackNotifyListener.class);

    @Autowired
    private CallbackServiceFactory callbackServiceFactory;

    public CallbackNotifyListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.CALLBACK_COMMON_NOTIFY)
    public void callbackNotify(Message message, Channel channel) {
        String callbackJson = new String(message.getBody(), StandardCharsets.UTF_8);
        logger.info("回调通知:{}", callbackJson);
        try {
            CallBackDTO callBackDTO = JsonUtil.convertToObject(callbackJson, CallBackDTO.class);
//            AbstractCallbackService callbackService = callbackServiceFactory.getCallbackService(callBackDTO.getFlowChannel());
         //   callbackService.processCallback(callBackDTO);
        } catch (Exception e) {
            logger.error("回调通知异常:", e);
            processException(callbackJson, message, e, "流量回调", getMqService()::submitCallbackCommonNotifyDelay);
        } finally {
            ackMsg(callbackJson, message, channel);
        }
    }

}

package com.maguo.loan.cash.flow.entrance.ppd.config;
import com.maguo.loan.cash.flow.common.RequestUriLogFilter;
import com.maguo.loan.cash.flow.entrance.ppd.filter.EncryptFilter;
import jakarta.servlet.Filter;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CompositeFilter;

import java.util.ArrayList;
import java.util.List;


@Configuration
public class PpdFilterConfig {

    @Bean
    public FilterRegistrationBean<CompositeFilter> ppdFilter(PpdConfig ppdConfig) {
        List<Filter> filterList = new ArrayList<>();
        filterList.add(new RequestUriLogFilter());
        filterList.add(new EncryptFilter(ppdConfig));
        CompositeFilter compositeFilter = new CompositeFilter();
        compositeFilter.setFilters(filterList);
        FilterRegistrationBean<CompositeFilter> bean = new FilterRegistrationBean<>(compositeFilter);
        bean.addUrlPatterns("/ppd/*");
        return bean;
    }

}

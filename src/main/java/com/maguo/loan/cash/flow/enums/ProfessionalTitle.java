package com.maguo.loan.cash.flow.enums;

public enum ProfessionalTitle {
    ONE(1, "无"),
    TWO(2, "初级"),
    THREE(3, "中级"),
    FOUR(4, "高级");
    private String desc;
    private Integer code;
    ProfessionalTitle(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    public Integer getCode() {
        return code;
    }
    public String getDesc() {
        return desc;
    }
}

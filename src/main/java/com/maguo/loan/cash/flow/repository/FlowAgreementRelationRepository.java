package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.FlowAgreementRelation;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface FlowAgreementRelationRepository extends JpaRepository<FlowAgreementRelation, String> {

    FlowAgreementRelation findByFlowChannelAndParam(FlowChannel flowChannel, String param);
    List<FlowAgreementRelation> findByFlowChannelAndParamIn(FlowChannel flowChannel, List<String> paramList);

    @Query("SELECT f FROM FlowAgreementRelation f WHERE f.flowChannel IS NULL AND f.param = :param")
    FlowAgreementRelation findByFlowChannelIsNullAndParam(String param);


}

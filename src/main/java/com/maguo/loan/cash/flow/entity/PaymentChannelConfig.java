package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.PaymentChannel;
import com.maguo.loan.cash.flow.enums.WhetherState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024-12-30
 */
@Entity
@Table(name = "payment_channel_config")
public class PaymentChannelConfig extends BaseEntity {

    /**
     * 支付通道编码
     */
    @Enumerated(EnumType.STRING)
    private PaymentChannel paymentChannelCode;

    /**
     * 支付通道名称
     */
    private String paymentChannelName;

    /**
     * 单笔额度上限
     */
    private BigDecimal singleAmountUpper;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private WhetherState enabled;

    public PaymentChannel getPaymentChannelCode() {
        return paymentChannelCode;
    }

    public void setPaymentChannelCode(PaymentChannel paymentChannelCode) {
        this.paymentChannelCode = paymentChannelCode;
    }

    public String getPaymentChannelName() {
        return paymentChannelName;
    }

    public void setPaymentChannelName(String paymentChannelName) {
        this.paymentChannelName = paymentChannelName;
    }

    public BigDecimal getSingleAmountUpper() {
        return singleAmountUpper;
    }

    public void setSingleAmountUpper(BigDecimal singleAmountUpper) {
        this.singleAmountUpper = singleAmountUpper;
    }

    public WhetherState getEnabled() {
        return enabled;
    }

    public void setEnabled(WhetherState enabled) {
        this.enabled = enabled;
    }

    @Override
    protected String prefix() {
        return "PCC";
    }
}

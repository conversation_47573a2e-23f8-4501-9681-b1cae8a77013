package com.maguo.loan.cash.flow.dto.pushmk;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class LoanApplyLog {

    /**
     * 贷款时间 yyyy-MM-dd HH:mm:ss
     */
    private String Time;

    /**
     * 贷款渠道
     */
    private String Channel;


    /**
     * 申请产品
     */
    private String Prod;


    /**
     * 审批结果
     */
    private String ApprovalResult;


    /**
     * 拒绝原因
     */
    private String RefusalReason;


    /**
     * 贷款金额
     */
    private BigDecimal Amount;

    /**
     * 贷款用途
     */
    private String Purpose;


    /**
     * 贷款周期
     */
    private String Cycle;

    public String getTime() {
        return Time;
    }

    public void setTime(String time) {
        Time = time;
    }

    public String getChannel() {
        return Channel;
    }

    public void setChannel(String channel) {
        Channel = channel;
    }

    public String getProd() {
        return Prod;
    }

    public void setProd(String prod) {
        Prod = prod;
    }

    public String getApprovalResult() {
        return ApprovalResult;
    }

    public void setApprovalResult(String approvalResult) {
        ApprovalResult = approvalResult;
    }

    public String getRefusalReason() {
        return RefusalReason;
    }

    public void setRefusalReason(String refusalReason) {
        RefusalReason = refusalReason;
    }

    public BigDecimal getAmount() {
        return Amount;
    }

    public void setAmount(BigDecimal amount) {
        Amount = amount;
    }

    public String getPurpose() {
        return Purpose;
    }

    public void setPurpose(String purpose) {
        Purpose = purpose;
    }

    public String getCycle() {
        return Cycle;
    }

    public void setCycle(String cycle) {
        Cycle = cycle;
    }
}

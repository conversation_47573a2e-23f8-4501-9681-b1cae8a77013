package com.maguo.loan.cash.flow.util;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 360按日记息还款计划
 */
public class RepayPlanItem {

    private Integer period;

    private LocalDate capitalRepayDate;

    private LocalDate customRepayDate;


    private BigDecimal capitalTotalAmt = BigDecimal.ZERO;


    private BigDecimal customTotalAmt = BigDecimal.ZERO;

    private BigDecimal principalAmt = BigDecimal.ZERO;

    /**
     * 利息
     */
    private BigDecimal interestAmt = BigDecimal.ZERO;

    /**
     * 融担费
     */
    private BigDecimal guaranteeAmt = BigDecimal.ZERO;

    /**
     * 咨询费
     */
    private BigDecimal consultAmt = BigDecimal.ZERO;


    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public LocalDate getCapitalRepayDate() {
        return capitalRepayDate;
    }

    public void setCapitalRepayDate(LocalDate capitalRepayDate) {
        this.capitalRepayDate = capitalRepayDate;
    }

    public LocalDate getCustomRepayDate() {
        return customRepayDate;
    }

    public void setCustomRepayDate(LocalDate customRepayDate) {
        this.customRepayDate = customRepayDate;
    }

    public BigDecimal getCapitalTotalAmt() {
        return capitalTotalAmt;
    }

    public void setCapitalTotalAmt(BigDecimal capitalTotalAmt) {
        this.capitalTotalAmt = capitalTotalAmt;
    }

    public BigDecimal getCustomTotalAmt() {
        return customTotalAmt;
    }

    public void setCustomTotalAmt(BigDecimal customTotalAmt) {
        this.customTotalAmt = customTotalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }


    @Override
    public String toString() {
        return "RepayPlan{period=" + period + ", capitalRepayDate=" + capitalRepayDate + ", capitalTotalAmt=" + capitalTotalAmt
            + ", principalAmt=" + principalAmt + ", interestAmt=" + interestAmt + ", customRepayDate=" + customRepayDate + ", customTotalAmt=" + customTotalAmt
            + ", principalAmt=" + principalAmt + ", interestAmt=" + interestAmt + ", guaranteeAmt=" + guaranteeAmt + ", consultAmt=" + consultAmt + '}';
    }
}

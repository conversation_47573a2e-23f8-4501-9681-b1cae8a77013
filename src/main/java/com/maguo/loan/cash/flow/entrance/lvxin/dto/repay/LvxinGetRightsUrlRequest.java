package com.maguo.loan.cash.flow.entrance.lvxin.dto.repay;

/**
 * @ClassName GetRightsUrlRequest
 * <AUTHOR>
 * @Description 获取权益地址
 * @Date 2024/5/24 14:01
 * @Version v1.0
 **/
public class LvxinGetRightsUrlRequest {
    private String partnerUserId;
    /**
     * 绿信借款请求订单号
     */
    private String loanGid;
    /**
     * LoanId
     */
    private String partnerOrderNo;
    /**
     * 绿信callback url
     */
    private String fromH5;
    /**
     * 场景
     * 1.权益介绍与签约
     * 2.权益费还款地址
     */
    private Integer scene;

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid(String loanGid) {
        this.loanGid = loanGid;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public String getFromH5() {
        return fromH5;
    }

    public void setFromH5(String fromH5) {
        this.fromH5 = fromH5;
    }

    public Integer getScene() {
        return scene;
    }

    public void setScene(Integer scene) {
        this.scene = scene;
    }
}

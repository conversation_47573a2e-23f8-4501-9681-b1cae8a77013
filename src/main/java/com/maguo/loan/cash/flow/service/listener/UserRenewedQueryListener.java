package com.maguo.loan.cash.flow.service.listener;


import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.RiskService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @since 2024-10-18
 */
@Component
public class UserRenewedQueryListener extends AbstractListener {

    private static final Logger logger = LoggerFactory.getLogger(RepayResultListener.class);

    @Autowired
    private RiskService riskService;

    public UserRenewedQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.RENEWED_QUERY, concurrency = "1-1")
    public void onListen(Message message, Channel channel) {
        String recordId = new String(message.getBody(), StandardCharsets.UTF_8);

        try {
            logger.info("监听续借标识查询:{}", recordId);
            riskService.renewedQuery(recordId);
        } catch (Exception e) {
            processException(recordId, message, e, "查询续借标识查询异常", getMqService()::submitRenewedQueryDelay);
        } finally {
            ackMsg(recordId, message, channel);
        }
    }
}

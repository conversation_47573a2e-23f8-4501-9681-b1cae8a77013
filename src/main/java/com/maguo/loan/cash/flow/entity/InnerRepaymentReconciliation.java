package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.ReccState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "inner_repayment_reconciliation")
public class InnerRepaymentReconciliation extends BaseEntity {
    private String recFileId;
    /**
     * core还款编号
     */
    private String repayNo;
    /**
     * 还款id
     */
    private String repayId;
    /**
     * 借据
     */
    private String loanId;
    /**
     * 还款日期
     */
    private LocalDate repayDate;
    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 期次
     */
    private Integer period;
    /**
     * 本金
     */
    private BigDecimal principalAmount;
    /**
     * 利息
     */
    private BigDecimal interestAmount;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmount;
    /**
     * 融担费
     */
    private BigDecimal guaranteeAmount;
    /**
     * 违约金
     */
    private BigDecimal breachAmount;
    /**
     * 状态
     */
    @Enumerated(EnumType.STRING)
    private ReccState recState;

    public String getRecFileId() {
        return recFileId;
    }

    public void setRecFileId(String recFileId) {
        this.recFileId = recFileId;
    }

    public String getRepayNo() {
        return repayNo;
    }

    public void setRepayNo(String repayNo) {
        this.repayNo = repayNo;
    }

    public String getRepayId() {
        return repayId;
    }

    public void setRepayId(String repayId) {
        this.repayId = repayId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public LocalDate getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(LocalDate repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public BigDecimal getPrincipalAmount() {
        return principalAmount;
    }

    public void setPrincipalAmount(BigDecimal principalAmount) {
        this.principalAmount = principalAmount;
    }

    public BigDecimal getInterestAmount() {
        return interestAmount;
    }

    public void setInterestAmount(BigDecimal interestAmount) {
        this.interestAmount = interestAmount;
    }

    public BigDecimal getPenaltyAmount() {
        return penaltyAmount;
    }

    public void setPenaltyAmount(BigDecimal penaltyAmount) {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getGuaranteeAmount() {
        return guaranteeAmount;
    }

    public void setGuaranteeAmount(BigDecimal guaranteeAmount) {
        this.guaranteeAmount = guaranteeAmount;
    }

    public BigDecimal getBreachAmount() {
        return breachAmount;
    }

    public void setBreachAmount(BigDecimal breachAmount) {
        this.breachAmount = breachAmount;
    }

    public ReccState getRecState() {
        return recState;
    }

    public void setRecState(ReccState recState) {
        this.recState = recState;
    }
}

package com.maguo.loan.cash.flow.convert;

import com.jinghang.capital.api.dto.credit.BindApplyDto;
import com.maguo.loan.cash.flow.entity.BindCardRecord;
import com.maguo.loan.cash.flow.entity.UserBankCard;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import com.maguo.loan.cash.flow.remote.nfsp.req.BindApplyReq;
import com.maguo.loan.cash.flow.remote.nfsp.req.BindConfirmReq;
import com.maguo.loan.cash.flow.service.bound.exchange.ExchangeCardApplyReq;
import com.maguo.loan.cash.flow.vo.bound.BindResultVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CardConvert {
    CardConvert INSTANCE = Mappers.getMapper(CardConvert.class);

    @Mapping(source = "name", target = "realName")
    @Mapping(target = "cardType", ignore = true)
    @Mapping(source = "bankCode", target = "code")
    @Mapping(source = "channel", target = "payType")
    BindApplyReq toCommonBindApply(BindCardRecord record);

    @Mapping(target = "dynamicCode", ignore = true)
    @Mapping(source = "confirmOrderNo", target = "orderNo")
    @Mapping(source = "name", target = "realName")
    @Mapping(target = "cardType", ignore = true)
    @Mapping(source = "bankCode", target = "code")
    @Mapping(source = "channel", target = "payType")
    BindConfirmReq toCommonBindConfirm(BindCardRecord record);

    @Mapping(source = "name", target = "cardName")
    @Mapping(source = "bankCardNo", target = "cardNo")
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "revision", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "createdTime", ignore = true)
    @Mapping(target = "updatedBy", ignore = true)
    @Mapping(target = "updatedTime", ignore = true)
    UserBankCard toUserBankCard(BindCardRecord record);

    BindResultVo toVo(BindCardRecord record);

    //List<BankInfoDto> toDto(List<BankList> listBank);

    BindApplyDto toBindApply(ExchangeCardApplyReq exchangeCardApplyReq);

    static String toProtocolChannel(ProtocolChannel channel) {
        return switch (channel) {
            case BF -> "BAOFOO";
            default -> "UNKNOWN";
        };
    }

    com.jinghang.capital.api.dto.ProtocolChannel toCoreProtocolChannel(ProtocolChannel channel);


}

package com.maguo.loan.cash.flow.entrance.lvxin.dto.loan;

import com.maguo.loan.cash.flow.entrance.lvxin.dto.credit.DeviceInfo;
import jakarta.validation.constraints.NotBlank;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description 借款申请
 * @Date 2024/5/21 17:30
 * @Version v1.0
 **/
public class LvxinLoanApplyRequest {
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    @NotBlank(message = "授信流水号不能为空")
    private String partnerUserId;
    @NotBlank(message = "放款订单号不能为空")
    private String loanGid;
    private String bankCardNo;
    private BigDecimal loanAmount;
    private Integer period;
    @NotBlank(message = "贷款用途不能为空")
    private String loanPurpose;
    private DeviceInfo deviceInfo;
    private String facialPhoto;
    private String periodType;

    private SyncBindCard syncBindCard;

    public String getPeriodType() {
        return periodType;
    }

    public void setPeriodType(String periodType) {
        this.periodType = periodType;
    }

    public SyncBindCard getSyncBindCard() {
        return syncBindCard;
    }

    public void setSyncBindCard(SyncBindCard syncBindCard) {
        this.syncBindCard = syncBindCard;
    }

    public class SyncBindCard {
        //payChannel	String	是	支付渠道	1:宝付
        //bindType	String	是	签约方式	1:共享协议码签约
        //cntNo		String	是	共享协议码
        //phoneNo	String	是	银行卡预留手机号
        //certType	String	是	开户人证件类型	 身份证
        //idNo	String	是	开户人证件号码
        //acctNo	String	是	银行卡号
        //acctName	String	是	账号开户名
        private String    payChannel;
        private String   bindType;
        private String    cntNo	;
        private String   phoneNo;
        private String   certType;
        private String    idNo;
        private String   acctNo;
        private String   acctName;
        private String   bankCode;
        private String   bankName;
        public String getPayChannel() {
            return payChannel;
        }

        public void setPayChannel(String payChannel) {
            this.payChannel = payChannel;
        }

        public String getBindType() {
            return bindType;
        }

        public void setBindType(String bindType) {
            this.bindType = bindType;
        }

        public String getCntNo() {
            return cntNo;
        }

        public void setCntNo(String cntNo) {
            this.cntNo = cntNo;
        }

        public String getPhoneNo() {
            return phoneNo;
        }

        public void setPhoneNo(String phoneNo) {
            this.phoneNo = phoneNo;
        }

        public String getCertType() {
            return certType;
        }

        public void setCertType(String certType) {
            this.certType = certType;
        }

        public String getIdNo() {
            return idNo;
        }

        public void setIdNo(String idNo) {
            this.idNo = idNo;
        }

        public String getAcctNo() {
            return acctNo;
        }

        public void setAcctNo(String acctNo) {
            this.acctNo = acctNo;
        }

        public String getAcctName() {
            return acctName;
        }

        public void setAcctName(String acctName) {
            this.acctName = acctName;
        }

        public String getBankCode() {
            return bankCode;
        }

        public void setBankCode(String bankCode) {
            this.bankCode = bankCode;
        }

        public String getBankName() {
            return bankName;
        }

        public void setBankName(String bankName) {
            this.bankName = bankName;
        }
    }

    public @NotBlank(message = "用户ID不能为空")String getUserId() {
        return userId;
    }

    public void setUserId(@NotBlank(message = "用户ID不能为空")String userId) {
        this.userId = userId;
    }

    public  @NotBlank(message = "授信流水号不能为空")String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId( @NotBlank(message = "授信流水号不能为空")String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public  @NotBlank(message = "放款订单号不能为空")String getLoanGid() {
        return loanGid;
    }

    public void setLoanGid( @NotBlank(message = "放款订单号不能为空")String loanGid) {
        this.loanGid = loanGid;
    }

    public String getBankCardNo() {
        return bankCardNo;
    }

    public void setBankCardNo(String bankCardNo) {
        this.bankCardNo = bankCardNo;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public  Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public     @NotBlank(message = "贷款用途不能为空")String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(    @NotBlank(message = "贷款用途不能为空")String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public DeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(DeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getFacialPhoto() {
        return facialPhoto;
    }

    public void setFacialPhoto(String facialPhoto) {
        this.facialPhoto = facialPhoto;
    }
}

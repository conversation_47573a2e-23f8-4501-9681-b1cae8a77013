package com.maguo.loan.cash.flow.util;

import java.time.LocalDate;
import java.time.Period;
import java.time.temporal.ChronoUnit;

public class AgeUtil {
    /**
     * 计算截至今日的周岁.
     *
     * @param birthDay 出生日期
     * @return 周岁
     */
    public static long calcAge(LocalDate birthDay) {
        return calcAge(birthDay, LocalDate.now());
    }

    /**
     * 计算到指定日期的周岁.
     *
     * @param birthDay    出生日期
     * @param expectedDay 指定日期
     * @return 周岁
     */
    public static long calcAge(LocalDate birthDay, LocalDate expectedDay) {
        return Math.abs(ChronoUnit.YEARS.between(birthDay, expectedDay));
    }


    /**
     * 通过身份证号码计算当前年龄
     *
     * @param idCard 身份证号码
     * @return 当前年龄
     */
    public static Integer calculateAge(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            throw new IllegalArgumentException("无效的身份证号码");
        }

        String birthDateStr = idCard.substring(6, 14);

        LocalDate birthDate = LocalDate.of(
            Integer.parseInt(birthDateStr.substring(0, 4)),
            Integer.parseInt(birthDateStr.substring(4, 6)),
            Integer.parseInt(birthDateStr.substring(6, 8))
        );

        LocalDate currentDate = LocalDate.now();

        return Period.between(birthDate, currentDate).getYears();
    }

}

package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserInfoExpand;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public interface UserInfoExpandRepository extends JpaRepository<UserInfoExpand, String> {

    @Query("select u from UserInfoExpand u where u.mobile=?1 ORDER BY u.createdTime DESC LIMIT 1")
    Optional<UserInfoExpand> findByPhoneOrderByCreatedTimeDesc(String phoneMd5);

    Optional<UserInfoExpand> findFirstByCertNoOrderByCreatedTimeDesc(String certNo);

    Optional<UserInfoExpand> findByMobile(String md5Mobile);
}

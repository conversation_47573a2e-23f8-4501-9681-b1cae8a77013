package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.PpdRebindRecord;
import com.maguo.loan.cash.flow.enums.BoundSide;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR> gale
 * @Description
 * @Date 2024/2/19 16:26
 */
public interface PpdRebindRecordRepository extends JpaRepository<PpdRebindRecord, String> {

    PpdRebindRecord findByCreditIdAndStateAndBoundSide(String creditId, ProcessState state, BoundSide boundSide);
}

package com.maguo.loan.cash.flow.entrance.ppd.common;


import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.lang.reflect.Method;

public class EnumDescriptionValidator implements ConstraintValidator<EnumDescriptionValid, String> {

    private Class<? extends Enum<?>> enumClass;

    @Override
    public void initialize(EnumDescriptionValid constraintAnnotation) {
        enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }
        try {
            Enum<?>[] enumConstants = enumClass.getEnumConstants();
            Method getDescription = enumClass.getMethod("getDescription");
            for (Enum<?> enumConstant : enumConstants) {
                String description = (String) getDescription.invoke(enumConstant);
                if (value.equals(description)) {
                    return true;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("EnumDescriptionValid validator error", e);
        }
        return false;
    }
}

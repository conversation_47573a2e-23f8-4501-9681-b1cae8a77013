package com.maguo.loan.cash.flow.entrance.common.controller;

import com.maguo.loan.cash.flow.entrance.common.dto.request.SftpRestoreFileReqDTO;
import com.maguo.loan.cash.flow.entrance.common.service.SftpRestoreFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * sftp恢复文件
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/jh/sftp")
public class SftpRestoreFileController {

    @Autowired
    private SftpRestoreFileService restoreFileService;

    /**
     * 恢复文件
     */
    @PostMapping("/restoreFile")
    public String restoreFile(@RequestBody SftpRestoreFileReqDTO request) {
        return restoreFileService.restoreFile(request);
    }
}

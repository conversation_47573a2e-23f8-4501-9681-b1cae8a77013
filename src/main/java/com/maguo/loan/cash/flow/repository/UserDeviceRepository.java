package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserDevice;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserDeviceRepository extends JpaRepository<UserDevice, String> {

    @Query("select d from UserDevice d where d.userId = ?1 order by d.updatedTime desc limit 1")
    UserDevice findByUserId(String userId);

}

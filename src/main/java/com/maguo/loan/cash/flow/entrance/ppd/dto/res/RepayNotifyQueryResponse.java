package com.maguo.loan.cash.flow.entrance.ppd.dto.res;

import com.maguo.loan.cash.flow.entrance.ppd.dto.CommonResult;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RepayNotifyQueryResponse extends CommonResult {

    /**
     * 入账信息
     */
    private OfflineInfo offlineInfos;

    // getter 和 setter 方法

    public OfflineInfo getOfflineInfos() {
        return offlineInfos;
    }

    public void setOfflineInfos(OfflineInfo offlineInfos) {
        this.offlineInfos = offlineInfos;
    }

    /**
     * 入账信息类
     */
    public static class OfflineInfo {
        /**
         * 实际入账本金
         */
        private BigDecimal accountingPrincipal = BigDecimal.ZERO;

        /**
         * 实际入账利息
         */
        private BigDecimal accountingInterest = BigDecimal.ZERO;

        /**
         * 实际入账融担费
         */
        private BigDecimal accountingZhibaoFee = BigDecimal.ZERO;

        /**
         * 实际入账罚息
         */
        private BigDecimal accountingOverdue = BigDecimal.ZERO;

        /**
         * 实际入账咨询费
         */
        private BigDecimal accountingPoundageFee = BigDecimal.ZERO;

        /**
         * 错误码
         */
        private String errCode;

        /**
         * 还款结果处理描述
         */
        private String msg;

        public BigDecimal getAccountingPrincipal() {
            return accountingPrincipal;
        }

        public void setAccountingPrincipal(BigDecimal accountingPrincipal) {
            this.accountingPrincipal = accountingPrincipal;
        }

        public BigDecimal getAccountingInterest() {
            return accountingInterest;
        }

        public void setAccountingInterest(BigDecimal accountingInterest) {
            this.accountingInterest = accountingInterest;
        }

        public BigDecimal getAccountingZhibaoFee() {
            return accountingZhibaoFee;
        }

        public void setAccountingZhibaoFee(BigDecimal accountingZhibaoFee) {
            this.accountingZhibaoFee = accountingZhibaoFee;
        }

        public BigDecimal getAccountingOverdue() {
            return accountingOverdue;
        }

        public void setAccountingOverdue(BigDecimal accountingOverdue) {
            this.accountingOverdue = accountingOverdue;
        }

        public BigDecimal getAccountingPoundageFee() {
            return accountingPoundageFee;
        }

        public void setAccountingPoundageFee(BigDecimal accountingPoundageFee) {
            this.accountingPoundageFee = accountingPoundageFee;
        }

        public String getErrCode() {
            return errCode;
        }

        public void setErrCode(String errCode) {
            this.errCode = errCode;
        }

        public String getMsg() {
            return msg;
        }

        public void setMsg(String msg) {
            this.msg = msg;
        }
    }
}


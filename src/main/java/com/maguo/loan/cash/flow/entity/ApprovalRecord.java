package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.Car;
import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.House;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.LoanPurpose;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.Position;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 进件记录
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "approval_record")
public class ApprovalRecord extends BaseEntity {
    /**
     * 合作方单号
     */
    private String partnerOrderNo;

    /**
     * 流量
     */
    @Enumerated(EnumType.STRING)
    private FlowChannel flowChannel;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String certNo;

    /**
     * 申请金额
     */
    private BigDecimal applyAmount;

    /**
     * 申请期数
     */
    private Integer applyPeriods;

    /**
     * 教育程度
     */
    @Enumerated(EnumType.STRING)
    private Education education;

    /**
     * 职业类别
     */
    @Enumerated(EnumType.STRING)
    private Position position;

    /**
     * 自有车辆
     */
    @Enumerated(EnumType.STRING)
    private Car car;

    /**
     * 婚姻
     */
    @Enumerated(EnumType.STRING)
    private Marriage marriage;

    /**
     * 月收入
     */
    private Integer income;

    /**
     * 行业
     */
    @Enumerated(EnumType.STRING)
    private Industry industry;

    /**
     * 住房类型
     */
    @Enumerated(EnumType.STRING)
    private House house;

    /**
     * 电子邮箱
     */
    private String email;

    /**
     * 居住地址
     */
    private String livingAddress;

    /**
     * 居住地址省code
     */
    private String livingProvinceCode;
    /**
     * 居住地址市code
     */
    private String livingCityCode;

    /**
     * 居住地址区code
     */
    private String livingDistrictCode;
    /**
     * 居住街道
     */
    private String livingStreet;

    /**
     * 工作地址
     */
    private String companyAddress;

    /**
     * 工作单位
     */
    private String company;

    /**
     * 公司地址省code
     */
    private String companyProvinceCode;

    /**
     * 公司地址市code
     */
    private String companyCityCode;

    /**
     * 公司地址区code
     */
    private String companyDistrictCode;
    /**
     * 工作街道
     */
    private String companyStreet;
    /**
     * 工作单位电话
     */
    private String companyPhone;
    /**
     * 借款用途
     */
    @Enumerated(EnumType.STRING)
    private LoanPurpose loanPurpose;

    /**
     * 经纬度
     */
    private String gps;

    /**
     * 联系人信息
     */
    private String relations;
    /**
     * 身份证国徽面照片原始链接
     */
    private String nationalEmblemImage;

    /**
     * 身份证人脸照原始链接
     */
    private String headImage;

    /**
     * 人脸照原始链接
     */
    private String faceImage;

    /**
     * 身份证国徽面照bucket
     */
    private String nationalEmblemBucket;

    /**
     * 身份证国徽面照ossKey
     */
    private String nationalEmblemOssKey;

    /**
     * 身份证人脸照bucket
     */
    private String headBucket;

    /**
     * 身份证人脸照ossKey
     */
    private String headOssKey;

    /**
     * 身份证有效期开始时间
     */
    private LocalDate certValidStart;

    /**
     * 身份证有效期结束时间
     */
    private LocalDate certValidEnd;

    /**
     * 身份证地址
     */
    private String certAddress;

    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    private Gender gender;

    /**
     * 民族
     */
    private String nation;

    /**
     * 签发机关
     */
    private String certSignOrg;

    /**
     * 活体照片bucket
     */
    private String faceBucket;

    /**
     * 活体照片ossKey
     */
    private String faceOssKey;

    /**
     * 活体相似度
     */
    private BigDecimal faceScore;

    /**
     * 活体供应商
     */
    private String faceSource;

    /**
     * 识别时间
     */
    private LocalDateTime faceCollectTime;

    /**
     * 设备信息
     */
    private String deviceInfo;
    /**
     * 申请时间
     */
    private LocalDateTime applyTime;

    public String getHeadImage() {
        return headImage;
    }

    public void setHeadImage(String headImage) {
        this.headImage = headImage;
    }

    public String getNationalEmblemImage() {
        return nationalEmblemImage;
    }

    public void setNationalEmblemImage(String nationalEmblemImage) {
        this.nationalEmblemImage = nationalEmblemImage;
    }

    public String getFaceImage() {
        return faceImage;
    }

    public void setFaceImage(String faceImage) {
        this.faceImage = faceImage;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public String getPartnerOrderNo() {
        return partnerOrderNo;
    }

    public void setPartnerOrderNo(String partnerOrderNo) {
        this.partnerOrderNo = partnerOrderNo;
    }

    public FlowChannel getFlowChannel() {
        return flowChannel;
    }

    public void setFlowChannel(FlowChannel flowChannel) {
        this.flowChannel = flowChannel;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public Integer getApplyPeriods() {
        return applyPeriods;
    }

    public void setApplyPeriods(Integer applyPeriods) {
        this.applyPeriods = applyPeriods;
    }

    public Education getEducation() {
        return education;
    }

    public void setEducation(Education education) {
        this.education = education;
    }

    public Position getPosition() {
        return position;
    }

    public void setPosition(Position position) {
        this.position = position;
    }

    public Car getCar() {
        return car;
    }

    public void setCar(Car car) {
        this.car = car;
    }

    public Marriage getMarriage() {
        return marriage;
    }

    public void setMarriage(Marriage marriage) {
        this.marriage = marriage;
    }

    public Integer getIncome() {
        return income;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public String getCompanyStreet() {
        return companyStreet;
    }

    public void setCompanyStreet(String companyStreet) {
        this.companyStreet = companyStreet;
    }

    public void setIncome(Integer income) {
        this.income = income;
    }

    public Industry getIndustry() {
        return industry;
    }

    public void setIndustry(Industry industry) {
        this.industry = industry;
    }

    public House getHouse() {
        return house;
    }

    public void setHouse(House house) {
        this.house = house;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }


    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public LoanPurpose getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(LoanPurpose loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getGps() {
        return gps;
    }

    public void setGps(String gps) {
        this.gps = gps;
    }

    public String getRelations() {
        return relations;
    }

    public void setRelations(String relations) {
        this.relations = relations;
    }

    public String getNationalEmblemBucket() {
        return nationalEmblemBucket;
    }

    public void setNationalEmblemBucket(String nationalEmblemBucket) {
        this.nationalEmblemBucket = nationalEmblemBucket;
    }

    public String getNationalEmblemOssKey() {
        return nationalEmblemOssKey;
    }

    public void setNationalEmblemOssKey(String nationalEmblemOssKey) {
        this.nationalEmblemOssKey = nationalEmblemOssKey;
    }

    public String getHeadBucket() {
        return headBucket;
    }

    public void setHeadBucket(String headBucket) {
        this.headBucket = headBucket;
    }

    public String getHeadOssKey() {
        return headOssKey;
    }

    public void setHeadOssKey(String headOssKey) {
        this.headOssKey = headOssKey;
    }

    public LocalDate getCertValidStart() {
        return certValidStart;
    }

    public void setCertValidStart(LocalDate certValidStart) {
        this.certValidStart = certValidStart;
    }

    public LocalDate getCertValidEnd() {
        return certValidEnd;
    }

    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }

    public String getCertAddress() {
        return certAddress;
    }

    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    public Gender getGender() {
        return gender;
    }

    public void setGender(Gender gender) {
        this.gender = gender;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getCertSignOrg() {
        return certSignOrg;
    }

    public void setCertSignOrg(String certSignOrg) {
        this.certSignOrg = certSignOrg;
    }

    public String getFaceBucket() {
        return faceBucket;
    }

    public void setFaceBucket(String faceBucket) {
        this.faceBucket = faceBucket;
    }

    public String getFaceOssKey() {
        return faceOssKey;
    }

    public void setFaceOssKey(String faceOssKey) {
        this.faceOssKey = faceOssKey;
    }

    public BigDecimal getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(BigDecimal faceScore) {
        this.faceScore = faceScore;
    }

    public String getFaceSource() {
        return faceSource;
    }

    public void setFaceSource(String faceSource) {
        this.faceSource = faceSource;
    }

    public LocalDateTime getFaceCollectTime() {
        return faceCollectTime;
    }

    public void setFaceCollectTime(LocalDateTime faceCollectTime) {
        this.faceCollectTime = faceCollectTime;
    }

    public String getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(String deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingDistrictCode() {
        return livingDistrictCode;
    }

    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    public String getLivingStreet() {
        return livingStreet;
    }

    public void setLivingStreet(String livingStreet) {
        this.livingStreet = livingStreet;
    }

    public String getCompanyProvinceCode() {
        return companyProvinceCode;
    }

    public void setCompanyProvinceCode(String companyProvinceCode) {
        this.companyProvinceCode = companyProvinceCode;
    }

    public String getCompanyCityCode() {
        return companyCityCode;
    }

    public void setCompanyCityCode(String companyCityCode) {
        this.companyCityCode = companyCityCode;
    }

    public String getCompanyDistrictCode() {
        return companyDistrictCode;
    }

    public void setCompanyDistrictCode(String companyDistrictCode) {
        this.companyDistrictCode = companyDistrictCode;
    }

    @Override
    protected String prefix() {
        return "AP";
    }
}

package com.maguo.loan.cash.flow.util;


import com.maguo.loan.cash.flow.enums.FlowChannel;
import com.maguo.loan.cash.flow.enums.RightsSupplier;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/10
 */
public final class ThreadLocalUtil {

    private ThreadLocalUtil() {
    }

    public static final ThreadLocal<FlowChannel> THREAD_LOCAL = new ThreadLocal<>();
    public static final ThreadLocal<List<RightsSupplier>> THREAD_LOCAL_RIGHTS_SUPPLIERS = new ThreadLocal<>();

    public static void setFlowChannel(FlowChannel flowChannel) {
        THREAD_LOCAL.set(flowChannel);
    }

    public static void setThreadLocalRightsSupplier(String rightsSupplier) {
        THREAD_LOCAL_RIGHTS_SUPPLIERS.set(RightsSupplier.getList(rightsSupplier));
    }

    public static FlowChannel getFlowChannel() {
        return THREAD_LOCAL.get();
    }

    public static List<RightsSupplier> getThreadLocalRightsSupplier() {
        return THREAD_LOCAL_RIGHTS_SUPPLIERS.get();
    }

    public static void removeFlowChannel() {
        THREAD_LOCAL.remove();
    }
}

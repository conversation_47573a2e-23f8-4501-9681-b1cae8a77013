package com.maguo.loan.cash.flow.repository;



import com.maguo.loan.cash.flow.entity.IdCardVerificationRecord;
import com.maguo.loan.cash.flow.enums.ProcessState;
import org.springframework.data.jpa.repository.JpaRepository;

public interface IdCardVerificationRecordRepository extends JpaRepository<IdCardVerificationRecord, String> {

    IdCardVerificationRecord findTopByCertNoAndNameAndRequestStateOrderByCreatedTimeDesc(String idCard, String name, ProcessState requestState);
}

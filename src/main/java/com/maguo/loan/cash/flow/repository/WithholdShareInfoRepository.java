package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.WithholdShareInfo;
import com.maguo.loan.cash.flow.enums.WhetherState;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface WithholdShareInfoRepository extends JpaRepository<WithholdShareInfo, String> {
    WithholdShareInfo findByWithholdIdAndMerchantNo(String withholdId, String merchantNo);
    List<WithholdShareInfo> findAllByWithholdId(String withholdId);
    List<WithholdShareInfo> findAllByWithholdIdAndMerchantConfirm(String withholdId, WhetherState merchantConfirm);
}

package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.RevolvingStrategyRelate;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;

/**
 * RevolvingStrategyBinning 持久层接口
 */
public interface RevolvingStrategyReleateRepository extends JpaRepository<RevolvingStrategyRelate, String> {

    List<RevolvingStrategyRelate> findAllByStrategyId(String strategyId);
}

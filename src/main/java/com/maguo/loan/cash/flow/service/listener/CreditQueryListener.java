package com.maguo.loan.cash.flow.service.listener;



import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.CreditService;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/5/31
 */
@Component
public class CreditQueryListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(CreditQueryListener.class);

    @Autowired
    private CreditService creditService;

    public CreditQueryListener(MqService mqService, WarningService mqWarningService) {
        super(mqService, mqWarningService);
    }

    @RabbitListener(queues = RabbitConfig.Queues.CREDIT_QUERY)
    public void listenCreditResult(Message message, Channel channel) {
        String creditId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("监听授信结果:{}", creditId);
            // service
            creditService.bankCreditResult(creditId);
        } catch (Exception e) {
            processException(creditId, message, e, "查询授信结果异常", getMqService()::submitCreditResultQueryDelay);
        } finally {
            ackMsg(creditId, message, channel);
        }
    }
}

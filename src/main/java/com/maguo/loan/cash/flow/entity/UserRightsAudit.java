package com.maguo.loan.cash.flow.entity;

import com.maguo.loan.cash.flow.enums.AuditState;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;
import java.time.LocalDateTime;

/**
 * 用户小卡退订审核表
 */
@Entity
@Table(name = "user_rights_audit")
public class UserRightsAudit extends BaseEntity {

    /**
     * 用户id
     */
    private String subscribeId;

    /**
     * 备注
     */
    private String reason;

    /**
     * 订阅时间
     */
    private LocalDateTime applyTime;

    private String applyBy;

    /**
     * 退订时间
     */
    private LocalDateTime auditTime;

    private String auditBy;

    /**
     * 订阅状态 待审核/审核拒绝/审核通过
     */
    @Enumerated(EnumType.STRING)
    private AuditState auditState;

    public String getSubscribeId() {
        return subscribeId;
    }

    public void setSubscribeId(String subscribeId) {
        this.subscribeId = subscribeId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public LocalDateTime getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(LocalDateTime applyTime) {
        this.applyTime = applyTime;
    }

    public LocalDateTime getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(LocalDateTime auditTime) {
        this.auditTime = auditTime;
    }

    public AuditState getAuditState() {
        return auditState;
    }

    public void setAuditState(AuditState auditState) {
        this.auditState = auditState;
    }

    public String getApplyBy() {
        return applyBy;
    }

    public void setApplyBy(String applyBy) {
        this.applyBy = applyBy;
    }

    public String getAuditBy() {
        return auditBy;
    }

    public void setAuditBy(String auditBy) {
        this.auditBy = auditBy;
    }

    @Override
    protected String prefix() {
        return "URA";
    }
}

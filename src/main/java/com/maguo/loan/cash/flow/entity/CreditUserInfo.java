package com.maguo.loan.cash.flow.entity;


import com.maguo.loan.cash.flow.enums.Education;
import com.maguo.loan.cash.flow.enums.Gender;
import com.maguo.loan.cash.flow.enums.Industry;
import com.maguo.loan.cash.flow.enums.Marriage;
import com.maguo.loan.cash.flow.enums.Position;
import com.maguo.loan.cash.flow.enums.ProtocolChannel;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "credit_user_info")
public class CreditUserInfo extends BaseEntity {
    private String userId;
    /**
     * 授信id
     */
    private String creditId;
    /**
     * 身份证
     */
    private String certNo;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 姓名
     */
    private String name;
    /**
     * 婚姻
     */
    @Enumerated(EnumType.STRING)
    private Marriage marriage;
    /**
     * 学历
     */
    @Enumerated(EnumType.STRING)
    private Education education;
    /**
     * A
     */
    private String acardScore;
    /**
     * B
     */
    private String bcardScore;
    /**
     * 月收入
     */
    private Integer income;
    /**
     * 行业
     */
    @Enumerated(EnumType.STRING)
    private Industry industry;
    /**
     * 职业
     */
    @Enumerated(EnumType.STRING)
    private Position position;
    /**
     * 电子邮箱
     */
    private String email;
    /**
     * 居住地
     */
    private String livingAddress;
    /**
     * 省
     */
    private String livingProvinceCode;
    /**
     * 市
     */
    private String livingCityCode;
    /**
     * 区
     */
    private String livingDistrictCode;
    /**
     * 街道
     */
    private String livingStreet;
    /**
     * 工作单位
     */
    private String unit;
    /**
     * 工作地址
     */
    private String unitAddress;
    /**
     * 省
     */
    private String unitProvinceCode;
    /**
     * 市
     */
    private String unitCityCode;
    /**
     * 区
     */
    private String unitDistrictCode;
    /**
     * 街道
     */
    private String unitStreet;
    /**
     * 地址
     */
    private String certAddress;
    /**
     * 签发机关
     */
    private String certSignOrg;
    /**
     * 有效期开始
     */
    private LocalDate certValidStart;
    /**
     * 有效期截止
     */
    private LocalDate certValidEnd;
    /**
     * 人头
     */
    private String headOssBucket;
    /**
     * 人头
     */
    private String headOssKey;
    /**
     * 国徽
     */
    private String nationOssBucket;
    /**
     * 国徽
     */
    private String nationOssKey;
    /**
     * 省
     */
    private String certProvinceCode;
    /**
     * 市
     */
    private String certCityCode;
    /**
     * 区
     */
    private String certDistrictCode;
    /**
     * 性别
     */
    @Enumerated(EnumType.STRING)
    private Gender certGender;
    /**
     * 民族
     */
    private String certNation;
    /**
     * 人脸通道
     */
    private String faceChannel;
    /**
     * 人脸时间
     */
    private LocalDateTime faceTime;
    /**
     * 人脸分
     */
    private BigDecimal faceScore;
    /**
     * bucket
     */
    private String faceOssBucket;
    /**
     * key
     */
    private String faceOssKey;
    /**
     * 机型
     */
    private String deviceModel;
    /**
     * 系统类型
     */
    private String deviceOsType;
    /**
     * 系统版本
     */
    private String deviceOsVersion;
    /**
     * 经纬度
     */
    private String deviceGps;
    /**
     * IP地址
     */
    private String deviceIp;
    /**
     * mac地址
     */
    private String deviceMac;
    /**
     * 卡号
     */
    private String cardNo;
    /**
     * 户名
     */
    private String cardName;
    /**
     * 预留手机号
     */
    private String cardPhone;
    /**
     * 银行编码
     */
    private String cardBankCode;
    /**
     * 银行名称
     */
    private String cardBankName;
    /**
     * 绑卡渠道
     */
    @Enumerated(EnumType.STRING)
    private ProtocolChannel cardChannel;
    /**
     * 绑卡主体
     */
    private String cardMerchantNo;
    /**
     * 绑卡协议号
     */
    private String cardAgreeNo;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }



    public String getAcardScore() {
        return acardScore;
    }

    public void setAcardScore(String acardScore) {
        this.acardScore = acardScore;
    }

    public String getBcardScore() {
        return bcardScore;
    }

    public void setBcardScore(String bcardScore) {
        this.bcardScore = bcardScore;
    }

    public Integer getIncome() {
        return income;
    }

    public void setIncome(Integer income) {
        this.income = income;
    }



    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingDistrictCode() {
        return livingDistrictCode;
    }

    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    public String getLivingStreet() {
        return livingStreet;
    }

    public void setLivingStreet(String livingStreet) {
        this.livingStreet = livingStreet;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getUnitAddress() {
        return unitAddress;
    }

    public void setUnitAddress(String unitAddress) {
        this.unitAddress = unitAddress;
    }

    public String getUnitProvinceCode() {
        return unitProvinceCode;
    }

    public void setUnitProvinceCode(String unitProvinceCode) {
        this.unitProvinceCode = unitProvinceCode;
    }

    public String getUnitCityCode() {
        return unitCityCode;
    }

    public void setUnitCityCode(String unitCityCode) {
        this.unitCityCode = unitCityCode;
    }

    public String getUnitDistrictCode() {
        return unitDistrictCode;
    }

    public void setUnitDistrictCode(String unitDistrictCode) {
        this.unitDistrictCode = unitDistrictCode;
    }

    public String getUnitStreet() {
        return unitStreet;
    }

    public void setUnitStreet(String unitStreet) {
        this.unitStreet = unitStreet;
    }

    public String getCertAddress() {
        return certAddress;
    }

    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    public String getCertSignOrg() {
        return certSignOrg;
    }

    public void setCertSignOrg(String certSignOrg) {
        this.certSignOrg = certSignOrg;
    }

    public LocalDate getCertValidStart() {
        return certValidStart;
    }

    public void setCertValidStart(LocalDate certValidStart) {
        this.certValidStart = certValidStart;
    }

    public LocalDate getCertValidEnd() {
        return certValidEnd;
    }

    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }

    public String getHeadOssBucket() {
        return headOssBucket;
    }

    public void setHeadOssBucket(String headOssBucket) {
        this.headOssBucket = headOssBucket;
    }

    public String getHeadOssKey() {
        return headOssKey;
    }

    public void setHeadOssKey(String headOssKey) {
        this.headOssKey = headOssKey;
    }

    public String getNationOssBucket() {
        return nationOssBucket;
    }

    public void setNationOssBucket(String nationOssBucket) {
        this.nationOssBucket = nationOssBucket;
    }

    public String getNationOssKey() {
        return nationOssKey;
    }

    public void setNationOssKey(String nationOssKey) {
        this.nationOssKey = nationOssKey;
    }

    public String getCertProvinceCode() {
        return certProvinceCode;
    }

    public void setCertProvinceCode(String certProvinceCode) {
        this.certProvinceCode = certProvinceCode;
    }

    public String getCertCityCode() {
        return certCityCode;
    }

    public void setCertCityCode(String certCityCode) {
        this.certCityCode = certCityCode;
    }

    public String getCertDistrictCode() {
        return certDistrictCode;
    }

    public void setCertDistrictCode(String certDistrictCode) {
        this.certDistrictCode = certDistrictCode;
    }

    public Gender getCertGender() {
        return certGender;
    }

    public void setCertGender(Gender certGender) {
        this.certGender = certGender;
    }

    public String getCertNation() {
        return certNation;
    }

    public void setCertNation(String certNation) {
        this.certNation = certNation;
    }

    public String getFaceChannel() {
        return faceChannel;
    }

    public void setFaceChannel(String faceChannel) {
        this.faceChannel = faceChannel;
    }

    public LocalDateTime getFaceTime() {
        return faceTime;
    }

    public void setFaceTime(LocalDateTime faceTime) {
        this.faceTime = faceTime;
    }

    public BigDecimal getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(BigDecimal faceScore) {
        this.faceScore = faceScore;
    }

    public String getFaceOssBucket() {
        return faceOssBucket;
    }

    public void setFaceOssBucket(String faceOssBucket) {
        this.faceOssBucket = faceOssBucket;
    }

    public String getFaceOssKey() {
        return faceOssKey;
    }

    public void setFaceOssKey(String faceOssKey) {
        this.faceOssKey = faceOssKey;
    }

    public String getDeviceModel() {
        return deviceModel;
    }

    public void setDeviceModel(String deviceModel) {
        this.deviceModel = deviceModel;
    }

    public String getDeviceOsType() {
        return deviceOsType;
    }

    public void setDeviceOsType(String deviceOsType) {
        this.deviceOsType = deviceOsType;
    }

    public String getDeviceOsVersion() {
        return deviceOsVersion;
    }

    public void setDeviceOsVersion(String deviceOsVersion) {
        this.deviceOsVersion = deviceOsVersion;
    }

    public String getDeviceGps() {
        return deviceGps;
    }

    public void setDeviceGps(String deviceGps) {
        this.deviceGps = deviceGps;
    }

    public String getDeviceIp() {
        return deviceIp;
    }

    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }

    public String getDeviceMac() {
        return deviceMac;
    }

    public void setDeviceMac(String deviceMac) {
        this.deviceMac = deviceMac;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getCardName() {
        return cardName;
    }

    public void setCardName(String cardName) {
        this.cardName = cardName;
    }

    public String getCardPhone() {
        return cardPhone;
    }

    public void setCardPhone(String cardPhone) {
        this.cardPhone = cardPhone;
    }

    public String getCardBankCode() {
        return cardBankCode;
    }

    public void setCardBankCode(String cardBankCode) {
        this.cardBankCode = cardBankCode;
    }

    public String getCardBankName() {
        return cardBankName;
    }

    public void setCardBankName(String cardBankName) {
        this.cardBankName = cardBankName;
    }

    public ProtocolChannel getCardChannel() {
        return cardChannel;
    }

    public void setCardChannel(ProtocolChannel cardChannel) {
        this.cardChannel = cardChannel;
    }

    public String getCardMerchantNo() {
        return cardMerchantNo;
    }

    public void setCardMerchantNo(String cardMerchantNo) {
        this.cardMerchantNo = cardMerchantNo;
    }

    public String getCardAgreeNo() {
        return cardAgreeNo;
    }

    public void setCardAgreeNo(String cardAgreeNo) {
        this.cardAgreeNo = cardAgreeNo;
    }

    public Marriage getMarriage() {
        return marriage;
    }

    public void setMarriage(Marriage marriage) {
        this.marriage = marriage;
    }

    public Education getEducation() {
        return education;
    }

    public void setEducation(Education education) {
        this.education = education;
    }

    public Industry getIndustry() {
        return industry;
    }

    public void setIndustry(Industry industry) {
        this.industry = industry;
    }

    public Position getPosition() {
        return position;
    }

    public void setPosition(Position position) {
        this.position = position;
    }

    @Override
    protected String prefix() {
        return "CI";
    }
}

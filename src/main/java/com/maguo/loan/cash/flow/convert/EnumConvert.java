package com.maguo.loan.cash.flow.convert;



import com.jinghang.capital.api.dto.Education;
import com.jinghang.capital.api.dto.FileType;
import com.jinghang.capital.api.dto.FlowChannel;
import com.jinghang.capital.api.dto.LoanPurpose;
import com.jinghang.capital.api.dto.Marriage;
import com.jinghang.capital.api.dto.ProcessStatus;
import com.jinghang.capital.api.dto.ProtocolChannel;
import com.jinghang.capital.api.dto.repay.RepayPurpose;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.ValueMapping;
import org.mapstruct.ValueMappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2023/7/19
 */
@Mapper
public interface EnumConvert {
    EnumConvert INSTANCE = Mappers.getMapper(EnumConvert.class);

    @ValueMappings({
        @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    })
    FileType toCoreApi(com.maguo.loan.cash.flow.enums.FileType fileType);

    @ValueMappings({
        @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    })
    com.maguo.loan.cash.flow.enums.FileType toCoreApi(FileType fileType);

    Marriage toCoreApi(com.maguo.loan.cash.flow.enums.Marriage marriage);

    Education toCoreApi(com.maguo.loan.cash.flow.enums.Education education);

    RepayPurpose toCoreApi(com.maguo.loan.cash.flow.enums.RepayPurpose repayPurpose);

    LoanPurpose toCoreApi(com.maguo.loan.cash.flow.enums.LoanPurpose loanPurpose);

    ProtocolChannel toCoreApi(com.maguo.loan.cash.flow.enums.ProtocolChannel protocolChannel);

    @ValueMappings({
        @ValueMapping(source = MappingConstants.ANY_REMAINING, target = MappingConstants.NULL)
    })
    FlowChannel toCoreApi(com.maguo.loan.cash.flow.enums.FlowChannel flowChannel);

    @ValueMapping(source = "SUCCESS", target = "SUCCEED")
    @ValueMapping(source = "FAIL", target = "FAILED")
    com.maguo.loan.cash.flow.enums.ProcessState toCashState(ProcessStatus processStatus);


}

package com.maguo.loan.cash.flow.remote.sign;

import com.maguo.loan.cash.flow.remote.sign.req.SignApplyReq;
import com.maguo.loan.cash.flow.remote.sign.res.ResultMsg;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "sing-service"  , fallback = SignService.EsignServiceFallBack.class)
public interface SignService {

    //个人认证+签章
    @PostMapping({"/esign/sign"})
    ResultMsg sign(@RequestBody SignApplyReq contractNo);

    //esign
    @GetMapping({"/esign/getSignUrl"})
    ResultMsg getSignUrl(@RequestParam("contractNo") String contractNo);


    @Component
    class EsignServiceFallBack implements SignService {
        private static final Logger logger = LoggerFactory.getLogger(SignService.class);
        @Override
        public ResultMsg sign(SignApplyReq contractNo) {
            logger.error("调用外部个人认证+签章,业务流程异常:", contractNo);
            return null;
        }

        @Override
        public ResultMsg getSignUrl(String contractNo) {
            logger.error("调用外部签章链接,业务流程异常:", contractNo);
            return null;
        }
    }
}

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/5
 * @describe XXL
 */
package com.maguo.loan.cash.flow.util;

import com.jinghang.common.http.HttpFramework;
import com.jinghang.common.http.HttpRequest;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.crypt.DigestUtil;
import com.maguo.loan.cash.flow.enums.OcrType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Map;
import java.util.Set;

public class OcrImageUtils {
    private static final Logger logger = LoggerFactory.getLogger(OcrImageUtils.class);


    public static final int IMAGE_MIN_WIDTH = 8;
    public static final int IMAGE_MAX_WIDTH = 4000;

    public static final int IMAGE_MIN_HEIGHT = 8;

    public static final int IMAGE_MAX_HEIGHT = 4000;

    public static final int IMAGE_SIZE = 5 * 1024 * 1024;


    private static final int FILE_BYTE = 1024;


    public static boolean isValidImage(MultipartFile image) {
        if (image == null) {
            throw new RuntimeException("图片为空");
        }
        // 校验图片格式
        String contentType = image.getContentType();
        if (!isValidImageFormat(contentType)) {
            return false;
        }

        // 校验图片尺寸
        try {
            BufferedImage bufferedImage = ImageIO.read(image.getInputStream());
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();
            if (!isValidImageSize(width, height)) {
                return false;
            }
        } catch (IOException e) {
            logger.error("校验图片格式错误");
            return false;
        }

        // 校验图片大小
        long size = image.getSize();
        if (!isValidImageFileSize(size)) {
            return false;
        }

        return true;
    }

    private static boolean isValidImageFormat(String contentType) {
        return contentType != null && (contentType.contains("jpeg") || contentType.contains("png")
            || contentType.contains("gif") || contentType.contains("bmp")
            || contentType.contains("tiff"));

    }

    private static boolean isValidImageSize(int width, int height) {
        return width > IMAGE_MIN_WIDTH && height > IMAGE_MIN_HEIGHT && width <= IMAGE_MAX_WIDTH && height <= IMAGE_MAX_HEIGHT;
    }

    private static boolean isValidImageFileSize(long size) {
        return size <= IMAGE_SIZE;
    }


    public static String getImageBase64(InputStream inputStream) {
        try {
            byte[] imageBytes = readImageFromStream(inputStream);
            String base64String = Base64.getEncoder().encodeToString(imageBytes);
            return base64String;
        } catch (IOException e) {
            throw new RuntimeException("Failed to get image base64.", e);
        }
    }

    private static byte[] readImageFromStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[FILE_BYTE];
        int bytesRead;

        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }

        return outputStream.toByteArray();
    }

    public static String generateSignature(OcrType idCard, String requestId, String timeStamp, String idNumber, String name, String apiKey, String apiSecret) {
        StringBuilder dataBuilder = new StringBuilder();
        if (idCard == OcrType.FACE) {
            dataBuilder.append(apiKey).append(idNumber).append(name).append(requestId).append(timeStamp).append(apiSecret);
        } else {
            dataBuilder.append(apiKey).append(requestId).append(timeStamp).append(apiSecret);
        }
        return DigestUtil.md5(dataBuilder.toString());
    }


    public static String postOcr(OcrType ocrType, Map<String, Object> params, String requestId, String url) {
        try {
            logger.info("{}post请求开始，id:{}", ocrType.getDesc(), requestId);
            String post = HttpUtil.post(HttpFramework.HTTPCLIENT5, url, params);
            logger.info("{}post请求结果{}", ocrType.getDesc(), post);
            return post;
        } catch (Exception e) {
            logger.error("{}post请求异常,requestId:{},{}", ocrType.getDesc(), requestId, e.getMessage());
            throw new RuntimeException("OcrPost请求异常", e);
        }

    }

    public static String postOcr(OcrType idCard, String bodyParams, Map<String, String> headParams, String url) {
        try {
            logger.info("{}post请求开始，id:{}", idCard.getDesc(), headParams.get("X-Ca-Nonce"));
            HttpRequest request = HttpRequest.post(url).body(bodyParams);
            Set<String> keySet = headParams.keySet();
            keySet.forEach(key -> request.addHeader(key, headParams.get(key)));
            String body = request.send().getBody();
            logger.info("{}post请求结果{}", idCard.getDesc(), body);
            return body;
        } catch (Exception e) {
            logger.error("{}post请求异常,requestId:{},{}", idCard.getDesc(), headParams.get("X-Ca-Nonce"), e.getMessage());
            throw new RuntimeException("OcrPost请求异常", e);
        }

    }


}

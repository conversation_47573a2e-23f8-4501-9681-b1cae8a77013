package com.maguo.loan.cash.flow.dto.user;

/**
 * <AUTHOR>
 * @since 2024-11-12
 */
public class UserInfoDTO {

    /**
     * 教育情况
     */
    private String education;

    /**
     * 婚姻情况
     */
    private String marriage;

    /**
     * 居住省份编码
     */
    private String livingProvinceCode;

    /**
     * 居住城市编码
     */
    private String livingCityCode;

    /**
     * 居住县区编码
     */
    private String livingDistrictCode;

    /**
     * 居住详细地址
     */
    private String livingAddress;

    /**
     * 月收入
     */
    private String monthIncome;

    /**
     * 行业类型
     */
    private String industryType;

    /**
     * 职业类型
     */
    private String workType;

    /**
     * 工作单位
     */
    private String workUnit;

    /**
     * 工作地址
     */
    private String workAddress;

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getMarriage() {
        return marriage;
    }

    public void setMarriage(String marriage) {
        this.marriage = marriage;
    }

    public String getLivingProvinceCode() {
        return livingProvinceCode;
    }

    public void setLivingProvinceCode(String livingProvinceCode) {
        this.livingProvinceCode = livingProvinceCode;
    }

    public String getLivingCityCode() {
        return livingCityCode;
    }

    public void setLivingCityCode(String livingCityCode) {
        this.livingCityCode = livingCityCode;
    }

    public String getLivingDistrictCode() {
        return livingDistrictCode;
    }

    public void setLivingDistrictCode(String livingDistrictCode) {
        this.livingDistrictCode = livingDistrictCode;
    }

    public String getLivingAddress() {
        return livingAddress;
    }

    public void setLivingAddress(String livingAddress) {
        this.livingAddress = livingAddress;
    }

    public String getMonthIncome() {
        return monthIncome;
    }

    public void setMonthIncome(String monthIncome) {
        this.monthIncome = monthIncome;
    }

    public String getIndustryType() {
        return industryType;
    }

    public void setIndustryType(String industryType) {
        this.industryType = industryType;
    }

    public String getWorkType() {
        return workType;
    }

    public void setWorkType(String workType) {
        this.workType = workType;
    }

    public String getWorkUnit() {
        return workUnit;
    }

    public void setWorkUnit(String workUnit) {
        this.workUnit = workUnit;
    }

    public String getWorkAddress() {
        return workAddress;
    }

    public void setWorkAddress(String workAddress) {
        this.workAddress = workAddress;
    }
}

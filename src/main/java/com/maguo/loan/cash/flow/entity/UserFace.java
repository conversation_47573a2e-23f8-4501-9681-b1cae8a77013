package com.maguo.loan.cash.flow.entity;

import jakarta.persistence.Entity;
import jakarta.persistence.Table;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2023/9/15
 */
@Entity
@Table(name = "user_face")
public class UserFace extends BaseEntity {
    private String userId;
    /**
     * 人脸通道
     */
    private String faceChannel;
    /**
     * 人脸时间
     */
    private LocalDateTime faceTime;
    /**
     * 人脸分
     */
    private BigDecimal faceScore;
    /**
     * bucket
     */
    private String ossBucket;
    /**
     * key
     */
    private String ossKey;

    //供应商厂商
    private String facialSupplier;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFaceChannel() {
        return faceChannel;
    }

    public void setFaceChannel(String faceChannel) {
        this.faceChannel = faceChannel;
    }

    public LocalDateTime getFaceTime() {
        return faceTime;
    }

    public void setFaceTime(LocalDateTime faceTime) {
        this.faceTime = faceTime;
    }

    public BigDecimal getFaceScore() {
        return faceScore;
    }

    public void setFaceScore(BigDecimal faceScore) {
        this.faceScore = faceScore;
    }

    public String getOssBucket() {
        return ossBucket;
    }

    public void setOssBucket(String ossBucket) {
        this.ossBucket = ossBucket;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    @Override
    protected String prefix() {
        return "FC";
    }

    public String getFacialSupplier() {
        return facialSupplier;
    }

    public void setFacialSupplier(String facialSupplier) {
        this.facialSupplier = facialSupplier;
    }
}

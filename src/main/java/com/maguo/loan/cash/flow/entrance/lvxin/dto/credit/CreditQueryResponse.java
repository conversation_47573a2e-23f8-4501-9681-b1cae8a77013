package com.maguo.loan.cash.flow.entrance.lvxin.dto.credit;



import com.maguo.loan.cash.flow.entrance.lvxin.dto.LvxinRequest;
import com.maguo.loan.cash.flow.entrance.lvxin.enums.LvxinApi;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName CreditQueryResponse
 * <AUTHOR>
 * @Description 授信结果查询 响应
 * @Date 2024/5/16 16:48
 * @Version v1.0
 **/
public class CreditQueryResponse implements LvxinRequest {

    /**
     * 绿信用户Id
     */
    private String userId;
    /**
     * 绿信授信流水号
     */
    private String partnerUserId;
    /**
     * 授信状态 0审核中，1通过，2拒绝
     */
    private Integer status;
    /**
     * 拒绝理由 - 拒绝时必填
     */
    private String reason;
    /**
     * 授信金额 - 通过时必填 - 单位 元
     */
    private BigDecimal creditAmount;
    /**
     * 可借金额 - 通过时必填 - 单位 元
     */
    private BigDecimal canBorrowAmount;
    /**
     * 可借期数 - 通过时必填
     */
    private List<Integer> terms;
    /**
     * 额度过期时间(时间戳秒)
     */
    private Integer expireTime;
    /**
     * 资金类型  1-apr，2-irr
     */
    private Integer rateType;
    /**
     * 是否权益用户: 0-否，1-是，默认0
     */
    private Integer privilege = 0;

    public Integer getPrivilege() {
        return privilege;
    }

    public void setPrivilege(Integer privilege) {
        this.privilege = privilege;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPartnerUserId() {
        return partnerUserId;
    }

    public void setPartnerUserId(String partnerUserId) {
        this.partnerUserId = partnerUserId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public BigDecimal getCreditAmount() {
        return creditAmount;
    }

    public void setCreditAmount(BigDecimal creditAmount) {
        this.creditAmount = creditAmount;
    }

    public BigDecimal getCanBorrowAmount() {
        return canBorrowAmount;
    }

    public void setCanBorrowAmount(BigDecimal canBorrowAmount) {
        this.canBorrowAmount = canBorrowAmount;
    }

    public List<Integer> getTerms() {
        return terms;
    }

    public void setTerms(List<Integer> terms) {
        this.terms = terms;
    }

    public Integer getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Integer expireTime) {
        this.expireTime = expireTime;
    }

    public Integer getRateType() {
        return rateType;
    }

    public void setRateType(Integer rateType) {
        this.rateType = rateType;
    }

    @Override
    public LvxinApi getApiType() {
        return LvxinApi.PUSH_CREDIT_RESULT;
    }
}

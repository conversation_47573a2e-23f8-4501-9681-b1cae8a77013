package com.maguo.loan.cash.flow.entrance.common.exception;


/**
 * <AUTHOR>
 */

public enum CommonApiResultCode {

    SUCCESSED("000000", "成功", false),
    BUSINESS_ERROR("10000", "业务异常", true),
    SYSTEM_ERROR("90000", "系统异常", true),
    PARAM_ERROR("44444", "参数校验异常", true),
    SYS_ERROR("50000", "系统异常", true),
    REPEAT_SUBMIT("40000", "不可重复提交", false),

    /**
     * 参数校验异常
     */
    PARAM_PARTNER_ORDER_NOT_EXIST("40001", "合作机构单号不能为空", false),
    PARAM_FLOW_CHANNEL_NOT_EXIST("40002", "该渠道不存在", false),
    PARAM_FAIL("40003", "参数不合法", false),
    PARAM_IMAGE_FORMAT_FAIL("40004", "imageFormat参数非法", false),
    PARAM_LINK_FAIL("40005", "链接非法", false),
    PARAM_REPAY_PURPOSE_FAIL("40006", "还款目的非法", false),
    PARAM_RIGHTSMARKING_FAIL("40007", "rightsMarking参数非法", false),
    PARAM_CERTVAILD_FAIL("40008", "身份证有效期开始时间不能大于结束时间", false),
    RELATION_PHONE_FAIL("40009", "联系人手机号不能相同", false),
    DEDUCTIONS_APPLY_FAIL("40010", "需要放款成功才可申请扣费", false),
    DEVICEINFO_FAIL("40011", "设备信息长度过长", false),
    RELATION_FAIL("40012", "联系人信息长度过长", false),

    /**
     * 业务异常
     */
    ORDER_NOT_EXIST("50001", "订单不存在", true),
    LOAN_NOT_EXIST("50002", "借款不存在", true),
    RIGHTS_REPAY_PLAN_NOT_EXIST("50003", "权益扣款计划不存在", true),
    PRE_ORDER_NOT_EXIST("50004", "预订单不存在", true),
    USER_RISK_RECORD("50005", "查询不到对应的进件审核记录", true),
    APPROVAL_NOT_EXIST("50006", "查询不到进件记录", true),
    FLOW_REPAY_RECORD("50007", "还款申请记录不存在", true),
    BIND_CARD_NOT_EXIST("50008", "查询不到绑卡记录", true),
    REPAY_PLAN_NOT_EXIST("50009", "查询不到还款计划", true),
    RIGHTS_BASE_PACKAGE_NOT_EXIST("50010", "查询不到对应权益", true),
    CALL_BACK_FAIL("50011", "回调失败", true),
    CALL_BACK_SIGN_FAIL("50012", "验签失败", true),
    DOWN_CERT_FAIL("50013", "下载身份证正反面异常", true),
    RIGHTS_CODE_FAIL("50014", "获取权益信息失败", true),
    SUBMIT_LOAN_NOT_EXIST("50015", "不存在借款申请记录", false),
    RIGHTS_NOT_SUBMIT_LOAN("50016", "未购买权益", false),
    RIGHTS_NO_REPAY("50017", "当前订单权益不可还款", false),
    QUERY_ERROR_REPAY("50018", "还款查询订单不存在", false),
    UPLOAD_FAIL("50001", "上传图片失败", true);

    private final String code;
    private final String msg;
    /**
     * 是否告警,默认告警
     * 在全局异常拦截器判断该字段为true时，则告警
     */
    private final Boolean isWarning;

    CommonApiResultCode(String code, String msg, Boolean isWarning) {
        this.code = code;
        this.msg = msg;
        this.isWarning = isWarning;
    }

    public Boolean getWarning() {
        return isWarning;
    }

    public String getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

    public static boolean isSuccess(String code) {
        return SUCCESSED.getCode().equals(code);
    }

    public static boolean verifySuccess(String code) {
        return SUCCESSED.getCode().equals(code);
    }
}

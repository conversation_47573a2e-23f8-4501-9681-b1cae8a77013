package com.maguo.loan.cash.flow.remote.sign.res;


/**
 * describe:
 *
 * <AUTHOR>
 * @date 2019/12/07
 */

public enum ResultCode {

    SUCCESS("200", "操作成功"),
    CONTRACT_NOT_EXIST("1001", "合同编号不能为空"),
    UNAUTHORIZED("401", "验签失败"),
    DATA_REPEAT("206", "数据重复"),

    /**
     * 从1001序列号开始，依次记录异常信息
     */
    SHOP_ID_NOT_NULL("1001", "shopId不能为空"),
    TILL_ID_NOT_NULL("1002", "tillId不能为空"),
    CLOSE_TYPE_NOT_NULL("1003", "closeType不能为空");;


    private String code;

    private String message;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    ResultCode(String code, String message) {
        this.code = code;
        this.message = message;
    }


}

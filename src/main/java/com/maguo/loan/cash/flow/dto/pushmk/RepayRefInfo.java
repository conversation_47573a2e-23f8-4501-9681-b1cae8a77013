package com.maguo.loan.cash.flow.dto.pushmk;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class RepayRefInfo {

    /**
     * 期次
     */
    private String periodNum;

    /**
     * 起息日期 (yyyy-MM-dd)
     */
    private String startDate;

    /**
     * 应还日期 (yyyy-MM-dd)
     */
    private String settleDate;

    /**
     * 还款日期 (yyyy-MM-dd)
     */
    private String tradeDate;

    /**
     * 应还总额 (元)
     */
    private BigDecimal needAmount;

    /**
     * 应还本金 (元)
     */
    private BigDecimal needCorpus;

    /**
     * 应还利息 (元)
     */
    private BigDecimal needAccrual;

    /**
     * 应还罚息 (元)
     */
    private BigDecimal needPunish;

    /**
     * 应还复息 (元)
     */
    private BigDecimal needRecvcomp;

    /**
     * 已还总额 (元)
     */
    private BigDecimal alreadyAmount;

    /**
     * 已还本金 (元)
     */
    private BigDecimal alreadyCorpus;

    /**
     * 已还利息 (元)
     */
    private BigDecimal alreadyAccrual;

    /**
     * 已还罚息 (元)
     */
    private BigDecimal alreadyPunish;

    /**
     * 已还复息 (元)
     */
    private BigDecimal alreadyRecvcomp;

    /**
     * 本金状态:
     * 1：正常
     */
    private String corpusStatus;

    /**
     * 手续费 (元)
     */
    private BigDecimal needFee;

    /**
     * 已还手续费 (元)
     */
    private BigDecimal alreadyFee;


    public String getPeriodNum() {
        return periodNum;
    }

    public void setPeriodNum(String periodNum) {
        this.periodNum = periodNum;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getSettleDate() {
        return settleDate;
    }

    public void setSettleDate(String settleDate) {
        this.settleDate = settleDate;
    }

    public String getTradeDate() {
        return tradeDate;
    }

    public void setTradeDate(String tradeDate) {
        this.tradeDate = tradeDate;
    }

    public BigDecimal getNeedAmount() {
        return needAmount;
    }

    public void setNeedAmount(BigDecimal needAmount) {
        this.needAmount = needAmount;
    }

    public BigDecimal getNeedCorpus() {
        return needCorpus;
    }

    public void setNeedCorpus(BigDecimal needCorpus) {
        this.needCorpus = needCorpus;
    }

    public BigDecimal getNeedAccrual() {
        return needAccrual;
    }

    public void setNeedAccrual(BigDecimal needAccrual) {
        this.needAccrual = needAccrual;
    }

    public BigDecimal getNeedPunish() {
        return needPunish;
    }

    public void setNeedPunish(BigDecimal needPunish) {
        this.needPunish = needPunish;
    }

    public BigDecimal getNeedRecvcomp() {
        return needRecvcomp;
    }

    public void setNeedRecvcomp(BigDecimal needRecvcomp) {
        this.needRecvcomp = needRecvcomp;
    }

    public BigDecimal getAlreadyAmount() {
        return alreadyAmount;
    }

    public void setAlreadyAmount(BigDecimal alreadyAmount) {
        this.alreadyAmount = alreadyAmount;
    }

    public BigDecimal getAlreadyCorpus() {
        return alreadyCorpus;
    }

    public void setAlreadyCorpus(BigDecimal alreadyCorpus) {
        this.alreadyCorpus = alreadyCorpus;
    }

    public BigDecimal getAlreadyAccrual() {
        return alreadyAccrual;
    }

    public void setAlreadyAccrual(BigDecimal alreadyAccrual) {
        this.alreadyAccrual = alreadyAccrual;
    }

    public BigDecimal getAlreadyPunish() {
        return alreadyPunish;
    }

    public void setAlreadyPunish(BigDecimal alreadyPunish) {
        this.alreadyPunish = alreadyPunish;
    }

    public BigDecimal getAlreadyRecvcomp() {
        return alreadyRecvcomp;
    }

    public void setAlreadyRecvcomp(BigDecimal alreadyRecvcomp) {
        this.alreadyRecvcomp = alreadyRecvcomp;
    }

    public String getCorpusStatus() {
        return corpusStatus;
    }

    public void setCorpusStatus(String corpusStatus) {
        this.corpusStatus = corpusStatus;
    }

    public BigDecimal getNeedFee() {
        return needFee;
    }

    public void setNeedFee(BigDecimal needFee) {
        this.needFee = needFee;
    }

    public BigDecimal getAlreadyFee() {
        return alreadyFee;
    }

    public void setAlreadyFee(BigDecimal alreadyFee) {
        this.alreadyFee = alreadyFee;
    }
}

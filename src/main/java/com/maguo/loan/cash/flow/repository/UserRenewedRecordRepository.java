package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.UserRenewedRecord;
import org.springframework.data.jpa.repository.JpaRepository;

/**
 * <AUTHOR>
 * @since 2024-10-17
 */
public interface UserRenewedRecordRepository extends JpaRepository<UserRenewedRecord, String> {
    UserRenewedRecord findTopByUserIdOrderByCreatedTimeDesc(String userId);

    UserRenewedRecord findByCustomRepayRecordId(String recordId);

    UserRenewedRecord findTopBySourceIdOrderByCreatedTimeDesc(String id);
}

package com.maguo.loan.cash.flow.util;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
* <AUTHOR>
* @Description 解析地址使用
* @Date 2025-05-25
* @Version 1.0
*/
public class ChineseAddressParser {
    // 增强版正则表达式，匹配中国行政区划
    private static final Pattern ADDRESS_PATTERN = Pattern.compile(
        "^(?<province>[^省]+省|.+自治区|.+特别行政区|.+市|.+省)?(?<city>[^市]+市|.+自治州|.+地区|.+盟)?(?<district>[^区]+区|.+县|.+旗|.+市|.+镇)?"
    );

    private static final String UNKNOWN = "未知";

    /**
     * 从地址中提取省、市、区三级信息
     * @param address 完整地址字符串
     * @return 包含省、市、区信息的Map，键为"province"、"city"、"district"
     */
    public static Map<String, String> parseChineseAddress(String address) {
        Map<String, String> result = new HashMap<>();
        result.put("province", UNKNOWN);
        result.put("city", UNKNOWN);
        result.put("district", UNKNOWN);

        if (address == null || address.trim().isEmpty()) {
            return result;
        }

        Matcher matcher = ADDRESS_PATTERN.matcher(address);

        if (matcher.find()) {
            // 提取省市区信息
            String province = matcher.group("province");
            String city = matcher.group("city");
            String district = matcher.group("district");

            // 处理直辖市情况（如北京市海淀区）
            if (province != null && province.endsWith("市") && city == null) {
                city = province;
                // 直辖市下辖区作为district
                if (district == null && matcher.group("district") != null) {
                    district = matcher.group("district");
                }
            }

            // 处理省直辖县级市情况（如河南省济源市）
            if (province != null && city != null && city.equals(province.replace("省", "市"))) {
                city = null;
            }

            // 设置结果，如果为null则保持默认的"未知"
            if (province != null && !province.trim().isEmpty()) {
                result.put("province", province.trim());
            }
            if (city != null && !city.trim().isEmpty()) {
                result.put("city", city.trim());
            }
            if (district != null && !district.trim().isEmpty()) {
                result.put("district", district.trim());
            }
        }

        return result;
    }

    /**
     * 获取省市区三级信息
     * @param address 完整地址字符串
     * @return 字符串数组，依次为省、市、区，不会返回null值
     */
    public static String[] getProvinceCityDistrict(String address) {
        Map<String, String> location = parseChineseAddress(address);
        return new String[]{
            location.get("province"),
            location.get("city"),
            location.get("district")
        };
    }

    /**
     * 获取城市和区名
     * @param address 完整地址字符串
     * @return 字符串数组，[0]=城市，[1]=区名，不会返回null值
     */
    public static String[] getCityAndDistrict(String address) {
        String[] pcd = getProvinceCityDistrict(address);
        // 处理直辖市情况
        if (UNKNOWN.equals(pcd[1]) && !UNKNOWN.equals(pcd[0]) && pcd[0].endsWith("市")) {
            return new String[]{pcd[0], pcd[2]};
        }
        return new String[]{pcd[1], pcd[2]};
    }

    /**
     * 尝试从地址中提取任意可用的行政区划信息
     * @param address 完整地址字符串
     * @return 字符串数组，按优先级返回找到的行政区划信息
     */
    public static String[] getAnyAdministrativeDivision(String address) {
        String[] result = new String[2];
        result[0] = UNKNOWN;
        result[1] = UNKNOWN;

        if (address == null || address.trim().isEmpty()) {
            return result;
        }

        String[] pcd = getProvinceCityDistrict(address);

        // 优先使用城市和区信息
        if (!UNKNOWN.equals(pcd[1])) {
            result[0] = pcd[1];
            if (!UNKNOWN.equals(pcd[2])) {
                result[1] = pcd[2];
            }
        }
        // 如果没有城市信息，但有省信息，尝试使用省信息
        else if (!UNKNOWN.equals(pcd[0])) {
            result[0] = pcd[0];
            // 如果省级是直辖市，尝试使用区信息
            if (pcd[0].endsWith("市") && !UNKNOWN.equals(pcd[2])) {
                result[1] = pcd[2];
            }
        }
        // 如果只有区/县信息
        else if (!UNKNOWN.equals(pcd[2])) {
            result[1] = pcd[2];
        }

        return result;
    }

}

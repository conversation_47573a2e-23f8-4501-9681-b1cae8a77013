package com.maguo.loan.cash.flow.entrance.ppd.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 拍拍贷 资方配置
 */
@Configuration
public class PpdZzlnCapitalConfig {

    /**
     * 中置辽农
     * 允许放款开始时间
     */
    @Value("${ppd.zzln.loan.start}")
    private String zzlnLoanStart;
    /**
     * 中置辽农
     * 允许放款结束时间
     */
    @Value("${ppd.zzln.loan.end}")
    private String zzlnLoanEnd;



    public String getZzlnLoanStart() {
        return zzlnLoanStart;
    }

    public String getZzlnLoanEnd() {
        return zzlnLoanEnd;
    }


}

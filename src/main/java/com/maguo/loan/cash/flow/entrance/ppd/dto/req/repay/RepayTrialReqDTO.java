package com.maguo.loan.cash.flow.entrance.ppd.dto.req.repay;

/**
 * 还款试算请求数据传输对象
 */
public class RepayTrialReqDTO {
    /**
     * 唯一放款请求流水号
     */
    private String loanReqNo;
    /**
     * 请求方代码：CJCYDL_PPD2/CJCYDL_PPD3
     */
    private String sourceCode;
    /**
     * 还款类型：03-一次性提前结清/02-提前当期
     */
    private String repayType;
    /**
     * 用户还款日期(yyyyMMdd)
     */
    private String repayDate;
    /**
     * 起始期次
     */
    private Integer repayTerm;

    public String getLoanReqNo() {
        return loanReqNo;
    }

    public void setLoanReqNo(String loanReqNo) {
        this.loanReqNo = loanReqNo;
    }

    public String getSourceCode() {
        return sourceCode;
    }

    public void setSourceCode(String sourceCode) {
        this.sourceCode = sourceCode;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public Integer getRepayTerm() {
        return repayTerm;
    }

    public void setRepayTerm(Integer repayTerm) {
        this.repayTerm = repayTerm;
    }

    // Getter和Setter方法...
}

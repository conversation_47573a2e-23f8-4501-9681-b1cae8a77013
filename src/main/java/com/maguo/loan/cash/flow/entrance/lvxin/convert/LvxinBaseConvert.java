package com.maguo.loan.cash.flow.entrance.lvxin.convert;


import com.maguo.loan.cash.flow.util.BaseConstants;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
public interface LvxinBaseConvert {

    static LocalDate toCertValid(String dateStr) {
        if (StringUtils.equals(dateStr, "长期")) {
            return BaseConstants.DEFAULT_LONG_CERT_END;
        }
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    static com.maguo.loan.cash.flow.enums.LoanPurpose toLoanPurpose(String loanPurpose) {
        if (StringUtils.isBlank(loanPurpose)) {
            return com.maguo.loan.cash.flow.enums.LoanPurpose.OTHER;
        }
        return switch (loanPurpose) {
            case "SHOPPING" -> com.maguo.loan.cash.flow.enums.LoanPurpose.SHOPPING;
            case "FAMILY_DECORATION" -> com.maguo.loan.cash.flow.enums.LoanPurpose.DECORATION;
            case "TOURIST_HOLIDAY" -> com.maguo.loan.cash.flow.enums.LoanPurpose.TOUR;
            case "EDUCATION_AND_TRAINING" -> com.maguo.loan.cash.flow.enums.LoanPurpose.EDUCATION;
            case "HEALTH_CARE" -> com.maguo.loan.cash.flow.enums.LoanPurpose.HEALTH;
            default -> com.maguo.loan.cash.flow.enums.LoanPurpose.OTHER;
        };
    }

}

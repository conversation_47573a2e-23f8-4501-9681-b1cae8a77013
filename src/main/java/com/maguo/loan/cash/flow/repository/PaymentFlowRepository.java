package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.PaymentFlow;
import com.maguo.loan.cash.flow.enums.PaymentStatusEnum;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/22
 */
public interface PaymentFlowRepository extends JpaRepository<PaymentFlow, String> {

    Optional<PaymentFlow> findByMerchantOutOrderNoAndPayStatus(String merchantOutOrderNo, PaymentStatusEnum payStatus);

    PaymentFlow findByOrderIdAndPayStatus(String orderId, PaymentStatusEnum payStatus);

    PaymentFlow findByOrderId(String orderId);

    PaymentFlow findByMerchantOutOrderNo(String merchantOutOrderNo);

    PaymentFlow findByAggregatePayId(String aggregatePayId);
}

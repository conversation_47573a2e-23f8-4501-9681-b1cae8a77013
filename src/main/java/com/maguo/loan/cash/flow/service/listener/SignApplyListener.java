package com.maguo.loan.cash.flow.service.listener;



import com.maguo.loan.cash.flow.config.RabbitConfig;
import com.maguo.loan.cash.flow.service.AbstractListener;
import com.maguo.loan.cash.flow.service.MqService;
import com.maguo.loan.cash.flow.service.WarningService;
import com.maguo.loan.cash.flow.service.agreement.AgreementService;
import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2023/6/3
 */
@Component
public class SignApplyListener extends AbstractListener {
    private static final Logger logger = LoggerFactory.getLogger(SignApplyListener.class);

    private AgreementService agreementService;

    public SignApplyListener(MqService mqService, WarningService mqWarningService, AgreementService agreementService) {
        super(mqService, mqWarningService);
        this.agreementService = agreementService;
    }

    @RabbitListener(queues = RabbitConfig.Queues.SIGN_APPLY)
    public void listenSignApply(Message message, Channel channel) {
        String signId = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            logger.info("申请协议签署:{}", signId);
            // service
            agreementService.signApply(signId);
        } catch (Exception e) {
            processException(signId, message, e, "申请协议签署异常", getMqService()::submitSignApplyDelay);
        } finally {
            ackMsg(signId, message, channel);
        }
    }

}

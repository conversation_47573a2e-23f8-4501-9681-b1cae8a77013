package com.maguo.loan.cash.flow.repository;

import com.maguo.loan.cash.flow.entity.BfChargeRecord;
import org.springframework.data.jpa.repository.JpaRepository;

public interface BfChargeRecordRepository extends JpaRepository<BfChargeRecord, String> {
    BfChargeRecord findByServerTransId(String serverTransId);

    // 查询进度最新的记录
    BfChargeRecord findFirstByServerTransIdOrderByStateDesc(String serverTransId);

}

package com.maguo.loan.cash.flow.repository;


import com.maguo.loan.cash.flow.entity.UserRegister;
import com.maguo.loan.cash.flow.enums.FlowChannel;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.List;
import java.util.Optional;


/**
 * <AUTHOR>
 * @date 2023/9/15
 */
public interface UserRegisterRepository extends JpaRepository<UserRegister, String> {

    UserRegister findTopByOpenIdAndSourceChannel(String openId, FlowChannel sourceChannel);

    /**
     * 取最早的一条注册记录
     *
     * @param userId
     * @return
     */
    Optional<UserRegister> findTopByUserIdOrderByCreatedTimeAsc(String userId);

    List<UserRegister> findAllByOpenIdAndSourceChannel(String openId, FlowChannel sourceChannel);

}

package com.maguo.loan.cash.flow.enums;

/**
 * <AUTHOR>
 */
public enum EducationTo {
    PRIMARY_SCHOOL("1", "小学"),
    JUNIOR_HIGH_SCHOOL("2", "初中"),
    HIGH_SCHOOL("3", "高中"),
    JUNIOR_COLLEGE("4", "专科"),
    COLLEGE("5", "本科"),
    MASTER("6", "研究生"),
    DOCTOR("7", "博士"),
    UNKNOWN("0", "未知");

    private final String code;
    private final String desc;

    EducationTo(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // Getter
    public String getCode() { return code; }
    public String getDesc() { return desc; }

    // 通过code反向查找枚举
    public static EducationTo fromCode(String code) {
        for (EducationTo value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}

package com.maguo.loan.cash.flow.service;

import com.zsjz.third.part.baofoo.BaoFuBindCardService;
import com.zsjz.third.part.baofoo.BaoFuBindConfirmService;
import com.zsjz.third.part.baofoo.BaoFuChargeQueryService;
import com.zsjz.third.part.baofoo.BaoFuChargeService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <AUTHOR> gale
 * @Classname ChargeServiceTest
 * @Description TODO
 * @Date 2025/5/23 17:27
 */
@SpringBootTest
class ChargeServiceTest {

    @Autowired
    private BaoFuBindCardService bindCardService;
    @Autowired
    private BaoFuBindConfirmService bindConfirmService;
    @Autowired
    private BaoFuChargeService chargeService;
    @Autowired
    private BaoFuChargeQueryService chargeQueryService;

    @Test
    void chargeMotherAndChild() {




    }
}

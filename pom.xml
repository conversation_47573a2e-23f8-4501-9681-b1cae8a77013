<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.jinghang.fin.core</groupId>
    <artifactId>loan-cash-capital</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <modules>
        <module>capital-core</module>
        <module>capital-batch</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.version>3.2.1</spring-boot.version>
        <!-- 修改spring boot版本时，需要同时修改fin-batch中lombok的版本 -->
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <java.version>17</java.version>
        <apollo.version>2.2.0</apollo.version>
        <!-- fin-api version -->
        <fin.api.version>25.04.15</fin.api.version>
        <common.util.version>24.08.08</common.util.version>
        <third.party.version>24.04.07</third.party.version>
        <fin-bank-outbound-api.version>24.10.11</fin-bank-outbound-api.version>
        <cycfc-client.version>1.4</cycfc-client.version>
        <jmnet.sign.api.version>24.05.30</jmnet.sign.api.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        <aliyun.oss.version>3.16.3</aliyun.oss.version>
        <huawei.obs.version>3.23.5</huawei.obs.version>
        <redisson.version>3.25.2</redisson.version>
        <commons-io.version>2.12.0</commons-io.version>
        <commons-compress.version>1.21</commons-compress.version>
        <jboss-marshalling-serial.version>2.0.12.Final</jboss-marshalling-serial.version>
        <deduction.version>24.11.28</deduction.version>
        <!-- 解决Apollo与Spring Boot 3.x的Guava版本冲突 -->
        <guava.version>32.1.3-jre</guava.version>
    </properties>

    <dependencyManagement>
        <dependencies>


            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.apollo</groupId>
                <artifactId>apollo-client-config-data</artifactId>
                <version>${apollo.version}</version>
            </dependency>
            <!-- 解决Apollo与Spring Boot 3.x的Guava版本冲突 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.oss.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>jaxb-api</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.huaweicloud</groupId>
                <artifactId>esdk-obs-java</artifactId>
                <version>${huawei.obs.version}</version>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.mwiede</groupId>
                <artifactId>jsch</artifactId>
                <version>0.2.17</version>
            </dependency>
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons-io.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>${commons-compress.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cycfc</groupId>
                <artifactId>cycfc_client</artifactId>
                <version>${cycfc-client.version}</version>
            </dependency>
            <dependency>
                <groupId>org.jboss.marshalling</groupId>
                <artifactId>jboss-marshalling-serial</artifactId>
                <version>${jboss-marshalling-serial.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.12.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <parameters>true</parameters>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>true</failsOnError>
                    <linkXRef>false</linkXRef>
                    <configLocation>checkstyle.xml</configLocation>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>10.9.3</version>
                    </dependency>
                </dependencies>
            </plugin>

        </plugins>
    </build>
</project>

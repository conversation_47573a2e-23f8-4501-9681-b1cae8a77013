package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.api.dto.StatusAble;
import com.jinghang.capital.api.dto.repay.RepayCategory;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayType;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class RepayBatchInfoVo extends StatusAble {

    /**
     * 资金放款id
     */
    private String loanId;

    /**
     * 期数
     */
    private Integer period;

    /**
     * 还款类型
     */
    private RepayType repayType;

    private LocalDateTime actRepayTime;

    /**
     * 还款分类
     */
    private RepayCategory repayCategory;

    /**
     * 还款目的
     */
    private RepayPurpose repayPurpose;

    /**
     * 还款模式
     */
    private RepayMode repayMode;

    /**
     * 还款金额
     */
    private BigDecimal totalAmt;

    /**
     * 还款本金
     */
    private BigDecimal principalAmt;
    /**
     * 融单费
     */
    private BigDecimal guaranteeFee;

    /**
     * 还款利息
     */
    private BigDecimal interestAmt;

    /**
     * 还款罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 减免金额
     */
    private BigDecimal reductAmt;

    /**
     * 失败原因
     */
    private String failMsg;

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public LocalDateTime getActRepayTime() {
        return actRepayTime;
    }

    public void setActRepayTime(LocalDateTime actRepayTime) {
        this.actRepayTime = actRepayTime;
    }


    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public RepayCategory getRepayCategory() {
        return repayCategory;
    }

    public void setRepayCategory(RepayCategory repayCategory) {
        this.repayCategory = repayCategory;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getReductAmt() {
        return reductAmt;
    }

    public void setReductAmt(BigDecimal reductAmt) {
        this.reductAmt = reductAmt;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public void setFailMsg(String failMsg) {
        this.failMsg = failMsg;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }
}

package com.jinghang.capital.core.banks.cybk.enums;

/**
 * @公司 中数金智(上海)有限公司
 * @作者 Mr.sandman
 * @时间 2025/06/25 14:35
 */
public enum CYBKResultType {

    PROCESSING("01", "处理中"),
    REJECTED("02", "拒绝"),
    CREDIT_APPROVED("03", "额度类通过"),
    NON_CREDIT_APPROVED("04", "非额度类通过");

    private final String code;
    private final String description;

    CYBKResultType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    public String getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }

    // 根据code获取对应的枚举值
    public static CYBKResultType fromCode(String code) {
        for (CYBKResultType status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("未知的准入状态code: " + code);
    }

}

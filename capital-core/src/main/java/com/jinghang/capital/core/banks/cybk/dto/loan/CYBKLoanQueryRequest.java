package com.jinghang.capital.core.banks.cybk.dto.loan;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLoanQueryRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.LOAN_QUERY;


    /**
     * 放款申请编号
     */
    private String outLoanSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银放款流水号
     */
    private String loanSeq;

    public String getOutLoanSeq() {
        return outLoanSeq;
    }

    public void setOutLoanSeq(String outLoanSeq) {
        this.outLoanSeq = outLoanSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanSeq() {
        return loanSeq;
    }

    public void setLoanSeq(String loanSeq) {
        this.loanSeq = loanSeq;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }


}

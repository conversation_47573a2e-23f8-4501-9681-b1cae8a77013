package com.jinghang.capital.core.dto;

public class RateLimiterDto {

    /**
     * 限流器名称
     */
    private final String name;

    private final String type;
    /**
     * 限流器速率（每秒请求次数）
     */
    private final long rate;

    public RateLimiterDto(String name, String type, long rate) {
        this.name = name;
        this.type = type;
        this.rate = rate;
    }

    public String getName() {
        return name;
    }

    public String getType() {
        return type;
    }

    public long getRate() {
        return rate;
    }
}

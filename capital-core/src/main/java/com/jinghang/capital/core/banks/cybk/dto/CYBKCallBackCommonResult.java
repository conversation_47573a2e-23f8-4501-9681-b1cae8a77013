package com.jinghang.capital.core.banks.cybk.dto;

import com.jinghang.common.util.JsonUtil;

public class CYBKCallBackCommonResult {

    private String resultCode;

    private String resultDesc;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultDesc() {
        return resultDesc;
    }

    public void setResultDesc(String resultDesc) {
        this.resultDesc = resultDesc;
    }

    /**
     * 成功返回
     */
    public static String success() {
        CYBKCallBackCommonResult result = new CYBKCallBackCommonResult();
        result.setResultCode("01");
        result.setResultDesc("接收成功");
        return JsonUtil.toJsonString(result);
    }

    /**
     * 失败返回
     */
    public static String fail() {
        CYBKCallBackCommonResult result = new CYBKCallBackCommonResult();
        result.setResultCode("02");
        result.setResultDesc("接收失败");
        return JsonUtil.toJsonString(result);
    }

    /**
     * 失败返回
     */
    public static String fail(String resultDesc) {
        CYBKCallBackCommonResult result = new CYBKCallBackCommonResult();
        result.setResultCode("02");
        result.setResultDesc(resultDesc);
        return JsonUtil.toJsonString(result);
    }
}

package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.dto.loan.CYBKLoanGuaranteeInfo;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoan;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKLoanReplanDTO;
import com.jinghang.capital.core.banks.cybk.service.CYBKLoanService;
import com.jinghang.capital.core.entity.Credit;
import com.jinghang.capital.core.entity.CustomerRepayRecord;
import com.jinghang.capital.core.entity.LoanReplan;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.vo.recc.ReccType;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:37
 * <p>
 * 长银直连 对客日终担保费文件
 * <p>
 */
@Component
public class CYBKCustDailyGuaranteeHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustDailyGuaranteeHandler.class);
    private static final int TWO = 2;
    private static final int SIX = 6;

    @Autowired
    private WarningService warningService;
    @Autowired
    private CYBKLoanService cybKLoanService;

    @Override
    public void process(LocalDate reccDay) {

        //获取日终发生变更的数据
        List<CYBKLoanReplanDTO> allList = findCYBKLoanPlanDailyHasChanged(reccDay);

        if (CollectionUtils.isEmpty(allList)) {
            logger.info("日期" + reccDay.toString() + " 没有待处理订单");
        }

        Map<String, CYBKCustDailyLoan> loanMap = getDailyLoanMap(allList, reccDay);

        File sourceFile = null;
        File tempDir = null;
        File okFile = null;
        try {

            Path path = Files.createTempDirectory("CYBK");
            tempDir = path.toFile();

            String filePre = DateFormatUtils.format(new Date(), "yyyyMMdd") + "对客日终担保费文件";
            sourceFile = File.createTempFile(tempDir.getAbsolutePath() + filePre, ".csv");
            CSVPrinter printer = CSVFormat.DEFAULT.withSkipHeaderRecord()
                    .withDelimiter(',').print(sourceFile, StandardCharsets.UTF_8);

            //表头
            // 合作机构贷款唯一编号,长银授信流水号,放款申请流水号,担保合同编号,担保合同状态,担保费(单位分),担保年费率,年基准天数,担保期数",
            // 每期担保费(单位分),签署日期,担保起始日期,担保终止日期,合同履行地,已归还担保费金额（单位分）,担保费余额（单位分）,核销标识（对客）,扩展信息;
            List<String> header = List.of(
                    "loan_seq", "appl_seq", "out_appl_seq", "contract_no", "status", "fee_amt", "fee_rate", "base_fee_rate_day", "tnr",
                    "period_fee_amt", "sign_date", "start_date", "end_date", "contract_execute_address", "paid_guarantee_fee_amt", "guarantee_fee_bal"
            );
            printer.printRecord(header);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (String loanId : loanMap.keySet()) {

                CYBKCustDailyLoan dailyLoan = loanMap.get(loanId);

                Credit credit = getCreditRepository().findById(dailyLoan.getOutApplSeq()).orElseThrow();
                CYBKLoanGuaranteeInfo guaranteeInfo = new CYBKLoanGuaranteeInfo();
                CYBKLoanGuaranteeInfo guarRate = cybKLoanService.getGuaranteeAmtAndRate(guaranteeInfo, credit);
                LoanReplan minReplan = getLoanReplanRepository().findByLoanIdAndPeriod(loanId, 1).orElseThrow();
                LoanReplan maxReplan = getLoanReplanRepository().findByLoanIdAndPeriod(loanId, dailyLoan.getPeriods()).orElseThrow();
                List<LoanReplan> planList = getLoanReplanRepository().findByLoanIdOrderByPeriod(loanId);
                //总担保费
                BigDecimal totalGuarantee = planList.stream().map(LoanReplan::getGuaranteeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                if ("CLEAR".equals(dailyLoan.getStatus())) {
                    //已结清, 应还融担费=实还融担费
                    List<CustomerRepayRecord> successRecord = getCustomerRepayRecordRepository().findByLoanIdAndRepayStatus(loanId, ProcessStatus.SUCCESS);
                    totalGuarantee = successRecord.stream().map(CustomerRepayRecord::getGuaranteeAmt).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                BigDecimal unpaidGuarantee = totalGuarantee.subtract(dailyLoan.getGuarantee());
                String loanTime = sdf.format(new Date(dailyLoan.getLoanTime().getTime()));

                List<String> custLoanDTOList = new ArrayList<>();
                custLoanDTOList.add(dailyLoan.getLoanSeq());
                custLoanDTOList.add(dailyLoan.getApplSeq());
                custLoanDTOList.add(dailyLoan.getOutApplSeq());
                custLoanDTOList.add(dailyLoan.getGuaranteeContractNo());
                custLoanDTOList.add(dailyLoan.getStatus());
                custLoanDTOList.add(totalGuarantee.movePointRight(TWO).toPlainString());
                custLoanDTOList.add(guarRate.getGuarRate().toPlainString());
                custLoanDTOList.add("360");
                custLoanDTOList.add(dailyLoan.getPeriods().toString());
                custLoanDTOList.add(minReplan.getGuaranteeAmt().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(loanTime);
                custLoanDTOList.add(loanTime);
                custLoanDTOList.add(maxReplan.getRepayDate().format(DateTimeFormatter.ISO_LOCAL_DATE));
                custLoanDTOList.add(getCybkConfig().getGuaranteeCompanyAddress(credit.getGuaranteeCompany()));
                custLoanDTOList.add(dailyLoan.getGuarantee().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(unpaidGuarantee.movePointRight(TWO).toPlainString());
                printer.printRecord(custLoanDTOList);
            }

            printer.close();

            String uploadPath = getCustReccFilePath(reccDay);

            String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "eod_guarantee_daily_" + dateStr + ".csv";
            String okFileName = "eod_guarantee_daily_" + dateStr + ".csv.ok";
            // 上传oss
            logger.info("长银直连上传对客日终担保费文件");

            // /download/cyxf/{产品编码}/out/files/{YYYYMMDD}/eod_loan_daily_${yyyymmdd}.csv
            getCybkSftpService().upload(uploadPath + fileName, sourceFile.getAbsolutePath().toString());

            // 生成 ok 文件
            Path localVerifyFilePath = Files.createTempFile("loan_" + dateStr, ".csv.ok");
            OutputStream verifyOs = Files.newOutputStream(localVerifyFilePath);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(String.valueOf(loanMap.keySet().size()).getBytes(StandardCharsets.UTF_8));
            IOUtils.copy(byteArrayInputStream, verifyOs);
            getCybkSftpService().upload(uploadPath + okFileName, localVerifyFilePath.toAbsolutePath().toString());
            okFile = localVerifyFilePath.toFile();
        } catch (Exception e) {
            logger.error("长银直连上传对客日终担保费文件异常", e);
            warningService.warn("长银直连上传对客日终担保费文件异常");
        } finally {
            if (sourceFile != null) {
                sourceFile.delete();
            }
            if (okFile != null) {
                okFile.delete();
            }
            if (tempDir != null) {
                tempDir.delete();
            }
        }
    }


    @Override
    public ReccType getReccType() {
        return ReccType.CUST_DAILY_GUARANTEE;
    }
}

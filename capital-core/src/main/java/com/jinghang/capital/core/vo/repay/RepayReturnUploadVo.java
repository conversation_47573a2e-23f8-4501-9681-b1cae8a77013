package com.jinghang.capital.core.vo.repay;

import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import java.time.LocalDate;

/**
 * 线下还款回盘文件上传
 *
 * <AUTHOR>
 * @date 2025-07-14 18:40
 */
public class RepayReturnUploadVo {
    /**
     * 资方渠道：HXBK  湖消蚂蚁
     */
    private BankChannel bankChannel;
    /**
     * 还款状态：SUCCESS,还款成功
     */
    private ProcessStatus repayStatus;
    /**
     * 实际还款日期
     */
    private LocalDate actualRepayDate;
    /**
     * 还款模式：OFFLINE ，线下还款
     */
    private RepayMode repayMode;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }


    public LocalDate getActualRepayDate() {
        return actualRepayDate;
    }

    public void setActualRepayDate(LocalDate actualRepayDate) {
        this.actualRepayDate = actualRepayDate;
    }

    public ProcessStatus getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(ProcessStatus repayStatus) {
        this.repayStatus = repayStatus;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }
}


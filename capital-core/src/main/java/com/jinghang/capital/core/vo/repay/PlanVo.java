package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.core.enums.BankChannel;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/5/18
 */
public class PlanVo {

    private BankChannel channel;
    private String loanId;
    private BigDecimal loanAmt;
    private Integer periods;
    private List<PlanItemVo> planItems;


    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public List<PlanItemVo> getPlanItems() {
        return planItems;
    }

    public void setPlanItems(List<PlanItemVo> planItems) {
        this.planItems = planItems;
    }
}

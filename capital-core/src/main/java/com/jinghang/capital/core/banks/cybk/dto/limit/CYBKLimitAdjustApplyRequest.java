package com.jinghang.capital.core.banks.cybk.dto.limit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLimitAdjustApplyRequest extends CYBKBaseRequest {

    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.LIMIT_ADJUST_APPLY;
    /**
     * 合作方调额批次号，32位字符串，必填
     */
    private String adjustNo;

    /**
     * 合作方调额流水号，32位字符串，必填
     */
    private String outAdjustNo;

    /**
     * 商户码值，32位字符串，必填，线下约定
     */
    private String merchantNo;

    /**
     * 门店码值，32位字符串，必填，线下约定
     */
    private String storeCode;

    /**
     * 贷款品种，20位字符串，必填，线下约定
     */
    private String loanType;

    /**
     * 长银授信流水号，32位字符串，必填
     */
    private String applCde;

    /**
     * 担保流水号，32位字符串，非必填
     */
    private String guarApplSeq;

    /**
     * 长银额度编号，32位字符串，非必填
     */
    private String lmtNo;

    /**
     * 业务类型，8位字符串，必填，取值范围：
     * ADJUST_AMT_APPLY:额度调整申请
     * ADJUST_AMT_UP_APPLY:额度调增
     * ADJUST_AMT_DOWN_APPLY:额度调减
     * ADJUST_RATE_APPLY:利率调整
     * ADJUST_RATE_UP_APPLY:利率调增
     * ADJUST_RATE_DOWN_APPLY:利率调减
     * CLEAR_UP_APPLY:清退(注销)
     * TMP_AMT_APPLY:临时额度
     * ADJUST_VALID_DATE:调整有效期
     * CREDIT_FROZEN:额度冻结
     * CREDIT_UNFROZEN:额度解冻
     */
    private String adjustType;

    /**
     * 调整前额度状态，8位字符串，非必填，取值范围：
     * NORM: 正常
     * FROZEN: 冻结
     * EXPIRE: 额度授权到期
     */
    private String oldLimitStatus;

    /**
     * 调整后额度状态，8位字符串，有条件必录，取值范围：
     * NORM: 正常
     * FROZEN: 冻结
     * EXPIRE: 额度授权到期
     * 当业务类型为调额额度时必录
     */
    private String newLimitStatus;

    /**
     * 调整前授信额度，16位数字，1位小数，非必填
     */
    private BigDecimal oldLimitAmt;

    /**
     * 调整后授信额度，16位数字，1位小数，有条件必录
     * 当业务类型为调额额度时必录
     */
    private BigDecimal newLimitAmt;

    /**
     * 临时额度，16位数字，1位小数，有条件必录
     * 当业务类型为调整临额时必录
     */
    private BigDecimal tempLimit;

    /**
     * 额度起始日期，16位字符串，非必填
     */
    private String startDate;

    /**
     * 额度起始时间，16位字符串，非必填
     */
    private String startTime;

    /**
     * 额度截止日期，16位字符串，有条件必录
     * 当业务类型为调整有效期时必录
     */
    private String endDate;

    /**
     * 额度截止时间，16位字符串，非必填
     */
    private String endTime;

    /**
     * 调整前定价，16位数字，6位小数，非必填，格式：年利率
     */
    private BigDecimal oldYearRate;

    /**
     * 调整后定价，16位数字，6位小数，有条件必录
     * 当业务模式为调价时候，必录
     */
    private BigDecimal newYearRate;

    /**
     * 额度调整原因，128位字符串，非必填
     */
    private String adjustReason;

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }

    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    public String getOutAdjustNo() {
        return outAdjustNo;
    }

    public void setOutAdjustNo(String outAdjustNo) {
        this.outAdjustNo = outAdjustNo;
    }

    public String getMerchantNo() {
        return merchantNo;
    }

    public void setMerchantNo(String merchantNo) {
        this.merchantNo = merchantNo;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getLoanType() {
        return loanType;
    }

    public void setLoanType(String loanType) {
        this.loanType = loanType;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getGuarApplSeq() {
        return guarApplSeq;
    }

    public void setGuarApplSeq(String guarApplSeq) {
        this.guarApplSeq = guarApplSeq;
    }

    public String getLmtNo() {
        return lmtNo;
    }

    public void setLmtNo(String lmtNo) {
        this.lmtNo = lmtNo;
    }

    public String getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    public String getOldLimitStatus() {
        return oldLimitStatus;
    }

    public void setOldLimitStatus(String oldLimitStatus) {
        this.oldLimitStatus = oldLimitStatus;
    }

    public String getNewLimitStatus() {
        return newLimitStatus;
    }

    public void setNewLimitStatus(String newLimitStatus) {
        this.newLimitStatus = newLimitStatus;
    }

    public BigDecimal getOldLimitAmt() {
        return oldLimitAmt;
    }

    public void setOldLimitAmt(BigDecimal oldLimitAmt) {
        this.oldLimitAmt = oldLimitAmt;
    }

    public BigDecimal getNewLimitAmt() {
        return newLimitAmt;
    }

    public void setNewLimitAmt(BigDecimal newLimitAmt) {
        this.newLimitAmt = newLimitAmt;
    }

    public BigDecimal getTempLimit() {
        return tempLimit;
    }

    public void setTempLimit(BigDecimal tempLimit) {
        this.tempLimit = tempLimit;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public BigDecimal getOldYearRate() {
        return oldYearRate;
    }

    public void setOldYearRate(BigDecimal oldYearRate) {
        this.oldYearRate = oldYearRate;
    }

    public BigDecimal getNewYearRate() {
        return newYearRate;
    }

    public void setNewYearRate(BigDecimal newYearRate) {
        this.newYearRate = newYearRate;
    }

    public String getAdjustReason() {
        return adjustReason;
    }

    public void setAdjustReason(String adjustReason) {
        this.adjustReason = adjustReason;
    }
}

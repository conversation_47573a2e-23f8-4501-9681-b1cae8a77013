package com.jinghang.capital.core.banks.cybk.config;


import com.cycfc.client.api.CycfcAPI;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKImageConfig implements InitializingBean, DisposableBean {

    @Value("${cybk.image.host}")
    private String cybkImageHost;

    @Value("${cybk.image.port}")
    private Integer cybkImagePort;

    @Value("${cybk.image.aesKey}")
    private String cybkImageAesKey;

    @Value("${cybk.image.privateKey}")
    private String cybkImagePrivateKey;

    public String getCybkImageHost() {
        return cybkImageHost;
    }

    public Integer getCybkImagePort() {
        return cybkImagePort;
    }

    public String getCybkImageAesKey() {
        return cybkImageAesKey;
    }

    public String getCybkImagePrivateKey() {
        return cybkImagePrivateKey;
    }

    @Override
    public void destroy() throws Exception {
        CycfcAPI.shutdown();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 初始化
        CycfcAPI.instance(this.getCybkImageHost(), this.getCybkImagePort(), getCybkImageAesKey(), getCybkImagePrivateKey());
    }
}

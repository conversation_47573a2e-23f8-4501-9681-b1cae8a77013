package com.jinghang.capital.core.banks.cybk.dto.repay;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayTrialRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.REPAY_TRIAL;

    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银借据号
     */
    private String loanNo;
    /**
     * 还款模式(还款模式为：01、06时，必录)
     */
    private String repaymentMode;
    /**
     * 期数
     */
    private String period;
    /**
     * 操作时间(yyyy-MM-dd HH:mm:ss)
     */
    private String operateTime;
    /**
     * 客户实际还款日期(非必填) yyyy-MM-dd
     */
    private String custSetlDt;

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getRepaymentMode() {
        return repaymentMode;
    }

    public void setRepaymentMode(String repaymentMode) {
        this.repaymentMode = repaymentMode;
    }

    public String getPeriod() {
        return period;
    }

    public void setPeriod(String period) {
        this.period = period;
    }

    public String getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(String operateTime) {
        this.operateTime = operateTime;
    }

    public String getCustSetlDt() {
        return custSetlDt;
    }

    public void setCustSetlDt(String custSetlDt) {
        this.custSetlDt = custSetlDt;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;

    }
}

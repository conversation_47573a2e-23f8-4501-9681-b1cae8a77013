package com.jinghang.capital.core.banks.cybk.enums;

/**
 * 长银直连接口返回码
 */
public enum CybkResponseStatus {
    SUCCESS("0000", "请求成功"),
    NOT_EXIST("D003", "没有对应业务流水号(业务不存在)");

    /**
     * 资方返回错误码
     */
    private final String respCode;

    /**
     * 错误描述
     */
    private final String desc;

    CybkResponseStatus(String respCode, String desc) {
        this.respCode = respCode;
        this.desc = desc;

    }

    public String getDesc() {
        return desc;
    }

    public String getRespCode() {
        return respCode;
    }
}

package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.enums.BankChannel;
import java.time.LocalDate;

public class SubstituteMarkApplyVo {

    /**
     * 代还日
     */
    private LocalDate substituteDay;
    /**
     * 资方
     */
    private BankChannel bankChannel;

    private GuaranteeCompany guaranteeCompany;

    public LocalDate getSubstituteDay() {
        return substituteDay;
    }

    public void setSubstituteDay(LocalDate substituteDay) {
        this.substituteDay = substituteDay;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }
}

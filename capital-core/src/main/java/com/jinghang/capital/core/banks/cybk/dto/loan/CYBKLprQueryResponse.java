package com.jinghang.capital.core.banks.cybk.dto.loan;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.math.BigDecimal;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLprQueryResponse {

    /**
     * 业务流水号
     */

    /**
     * 上浮点数 例：14.3%
     */
    private String floatPoint;

    /**
     * lpr发布日期
     */
    private String lprDate;

    /**
     * lpr利率
     */
    private BigDecimal lprRate;

    public String getFloatPoint() {
        return floatPoint;
    }

    public void setFloatPoint(String floatPoint) {
        this.floatPoint = floatPoint;
    }

    public String getLprDate() {
        return lprDate;
    }

    public void setLprDate(String lprDate) {
        this.lprDate = lprDate;
    }

    public BigDecimal getLprRate() {
        return lprRate;
    }

    public void setLprRate(BigDecimal lprRate) {
        this.lprRate = lprRate;
    }
}

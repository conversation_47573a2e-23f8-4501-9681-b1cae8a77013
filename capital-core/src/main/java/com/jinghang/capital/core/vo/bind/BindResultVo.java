package com.jinghang.capital.core.vo.bind;

import com.jinghang.capital.core.enums.SignStatus;
import com.jinghang.capital.core.vo.StatusAbleVo;

public class BindResultVo extends StatusAbleVo {
    /**
     * 绑卡id
     */
    private String sysId;

    private String msg;

    /**
     * 是否已签约
     */
    private SignStatus signStatus;

    /**
     * 签约协议号
     */
    private String agreementNo;

    public String getSysId() {
        return sysId;
    }

    public BindResultVo setSysId(String sysId) {
        this.sysId = sysId;
        return this;
    }

    public SignStatus getSignStatus() {
        return signStatus;
    }

    public void setSignStatus(SignStatus signStatus) {
        this.signStatus = signStatus;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getAgreementNo() {
        return agreementNo;
    }

    public void setAgreementNo(String agreementNo) {
        this.agreementNo = agreementNo;
    }
}

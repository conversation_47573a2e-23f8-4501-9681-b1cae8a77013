package com.jinghang.capital.core.dto;

import com.jinghang.capital.core.enums.FileType;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;

/**
 * 放款后资方协议下载申请
 */
public class CovenantDownApplyDto {

    /**
     * 借据id
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 文件类型
     */
    @Enumerated(EnumType.STRING)
    private FileType fileType;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }
}

package com.jinghang.capital.core.vo.repay;

import com.jinghang.capital.api.dto.ClaimResult;
import com.jinghang.capital.core.vo.StatusAbleVo;
import java.math.BigDecimal;
import java.time.LocalDateTime;

public class ActiveClaimQueryResultVo extends StatusAbleVo {
    /**
     * 代偿结果
     */
    private ClaimResult claimResult;
    /**
     * 代偿结果描述
     */
    private String msg;
    /**
     * 代偿申请编号
     */
    private String outerClaimId;
    /**
     * 资金系统还款id
     */
    private String claimId;
    /**
     * 代偿总金额
     */
    private BigDecimal amount;
    /**
     * 本金
     */
    private BigDecimal principal;
    /**
     * 利息
     */
    private BigDecimal interest;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 融担费
     */
    private BigDecimal guaranteeFee;
    /**
     * 咨询费
     */
    private BigDecimal consultFee;
    /**
     * 违约金
     */
    private BigDecimal breachFee;
    /**
     * 实还时间
     */
    private LocalDateTime actClaimTime;

    public ClaimResult getClaimResult() {
        return claimResult;
    }

    public void setClaimResult(ClaimResult claimResult) {
        this.claimResult = claimResult;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public String getOuterClaimId() {
        return outerClaimId;
    }

    public void setOuterClaimId(String outerClaimId) {
        this.outerClaimId = outerClaimId;
    }

    public String getClaimId() {
        return claimId;
    }

    public void setClaimId(String claimId) {
        this.claimId = claimId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreachFee() {
        return breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }

    public LocalDateTime getActClaimTime() {
        return actClaimTime;
    }

    public void setActClaimTime(LocalDateTime actClaimTime) {
        this.actClaimTime = actClaimTime;
    }
}

package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.CYBKReccLoan;
import com.jinghang.capital.core.entity.CYBKReconcileFile;
import com.jinghang.capital.core.entity.Loan;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.vo.recc.ReccType;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 对应长银借据明细文件
 */
@Component
public class CYBKReccLoanHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccLoanHandler.class);

    @Override
    public void process(LocalDate reccDay) {
        List<Loan> reccLoans = findReccLoans(reccDay);
        CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, CYBKReccFileType.LOAN_FILE);
        reconcileFile.setReccDate(LocalDate.now());
        String reccId = reconcileFile.getId();
        //查询成功的资方记录
        List<CYBKReccLoan> reccLoanFileRecords = findReccLoanFileRecords(reccId);
        if (reccLoans.size() == 0 && reccLoanFileRecords.size() == 0) {
            reconcileFile.setReccState(ReccStateEnum.S.name());
            updateCYBKReconcileFile(reconcileFile);
            return;
        }
        if (reccLoans.size() != reccLoanFileRecords.size()) {
            logger.warn("长银直连对账成功条数不一致 reccType：{} reccDay：{} 业务方条数：{} 资方条数：{}", CYBKReccFileType.LOAN_FILE, reccDay, reccLoans.size(),
                    reccLoanFileRecords.size());
            getWarningService().warn("\n长银直连对账:" + CYBKReccFileType.LOAN_FILE + "\n对账日:" + reccDay + "\n成功条数不一致 ");
        }

        List<CYBKReccLoan> successList = new ArrayList<>();
        reccLoanFileRecords.forEach(lf -> {
            String loanId = lf.getSysId();
            if (StringUtils.isBlank(loanId)) {
                lf.setReccStatus(ReccStateEnum.F.name());
                lf.setRemark("长银放款成功记录,系统未匹配到");
                updateReccLoan(lf);
                warningLog(lf, null, null);
                return;
            }
            Loan existRecord = filterLoanInter(reccLoans, loanId);
            boolean match = match(lf, existRecord);
            lf.setReccStatus(match ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
            if (match) {
                successList.add(lf);
            } else {
                //对账失败打印日志
                warningLog(lf, existRecord, loanId);
            }
            updateReccLoan(lf);
        });
        boolean allMatch = successList.size() == reccLoans.size() && successList.size() == reccLoanFileRecords.size();
        reconcileFile.setReccState(allMatch ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
        updateCYBKReconcileFile(reconcileFile);

        //对账失败，企业微信告警
        if (ReccStateEnum.F.name().equals(reconcileFile.getReccState())) {
            getWarningService().warn("\n长银直连对账失败:" + CYBKReccFileType.LOAN_FILE + "\n对账日:" + reccDay + "\n，对账成功总数与系统放款成功总数不一致 ");
        }
    }

    private void warningLog(CYBKReccLoan lf, Loan existRecord, String loanId) {
        BigDecimal sysAmount = null;
        Integer sysPeriod = null;
        if (Objects.nonNull(existRecord)) {
            sysAmount = existRecord.getLoanAmt();
            sysPeriod = existRecord.getPeriods();
        }
        logger.warn("长银直连对账失败，reccType：{} 资方loanId：{}，金额:{},期数:{}; 业务方金额:{},期数:{}",
                CYBKReccFileType.LOAN_FILE, loanId, lf.getAmount(), lf.getPeriod(), sysAmount, sysPeriod);
    }


    private Loan filterLoanInter(List<Loan> loanList, String loanId) {
        return loanList.stream().filter(l -> loanId.equals(l.getId())).findAny().orElse(null);
    }

    private boolean match(CYBKReccLoan reccLoan, Loan loan) {
        if (loan == null) {
            return false;
        }
        return loan.getLoanAmt().compareTo(reccLoan.getAmount()) == 0 && loan.getPeriods().equals(reccLoan.getPeriod());
    }

    @Override
    public ReccType getReccType() {
        return ReccType.LOAN;
    }
}

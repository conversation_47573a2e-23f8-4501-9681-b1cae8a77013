package com.jinghang.capital.core.banks.cybk.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/10 14:53
 **/
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRemoteRequest<T> implements Serializable {
    private static final long serialVersionUID = 5672992875440971608L;
    private CYBKRequestHeader head;
    private T body;

    public CYBKRequestHeader getHead() {
        return head;
    }

    public void setHead(CYBKRequestHeader head) {
        this.head = head;
    }

    public T getBody() {
        return this.body;
    }

    public void setBody(T body) {
        this.body = body;
    }
}

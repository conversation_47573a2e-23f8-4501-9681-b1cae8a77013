<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.AggregatePaymentRecordMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.AggregatePaymentRecord">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="repayPurpose" column="repay_purpose" jdbcType="VARCHAR"/>
        <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
        <result property="period" column="period" jdbcType="INTEGER"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="payTime" column="pay_time" jdbcType="TIMESTAMP"/>
        <result property="payResult" column="pay_result" jdbcType="VARCHAR"/>
        <result property="applyState" column="apply_state" jdbcType="VARCHAR"/>
        <result property="principalAmt" column="principal_amt" jdbcType="DECIMAL"/>
        <result property="interestAmt" column="interest_amt" jdbcType="DECIMAL"/>
        <result property="guaranteeAmt" column="guarantee_amt" jdbcType="DECIMAL"/>
        <result property="penaltyAmt" column="penalty_amt" jdbcType="DECIMAL"/>
        <result property="consultFee" column="consult_fee" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="reduceAmount" column="reduce_amount" jdbcType="DECIMAL"/>
        <result property="actAmount" column="act_amount" jdbcType="DECIMAL"/>
        <result property="payChannel" column="pay_channel" jdbcType="VARCHAR"/>
        <result property="productChannel" column="product_channel" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,repay_purpose,loan_id,order_id,user_id,pay_channel,
        name,mobile,period,apply_time,pay_time,pay_result,product_channel
        apply_state,principal_amt,interest_amt,guarantee_amt,
        penalty_amt,consult_fee,amount,reduce_amount,act_amount,remark,
        revision,created_by,created_time,updated_by,updated_time
    </sql>

    <sql id="page">
        a.id, a.order_id, a.name, a.mobile, a.period, a.apply_state, a.apply_time, a.pay_time, a.pay_result, a.amount, a.reduce_amount, a.act_amount, a.pay_channel, a.product_channel,
        a.remark, a.pay_url
    </sql>
    <!--条件查询聚合支付记录-->
    <select id="queryList" resultType="com.jinghang.cash.modules.manage.vo.rsp.AggregatePaymentRecordResp">
        select
        <include refid="page"/>
            from aggregate_payment_record a where 1=1
        <if test="orderId != null and orderId !=''">
            and order_id =#{orderId}
        </if>
        <if test="mobile != null and mobile !=''">
            and mobile =#{mobile}
        </if>
        <if test="payResult != null and payResult !=''">
            and pay_result =#{payResult}
        </if>
        <if test="startTime != null ">
            and apply_time  <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and apply_time  <![CDATA[<]]> #{endTime}
        </if>
        <if test="productChannels !=null and productChannels.size() >0">
            and product_channel in
            <foreach collection="productChannels" item="productChannels" close=")" separator="," open="(">
                #{productChannels}
            </foreach>
        </if>
        order by apply_time desc
    </select>

    <!--条件查询聚合支付记录-->
    <select id="queryListByPayerAcctCode" resultType="com.jinghang.cash.modules.manage.vo.rsp.AggregatePaymentRecordResp">
        select
        <include refid="page"/>
        from aggregate_payment_record a left join payment_flow p on a.id = p.aggregate_pay_id where p.payer_acct_code = #{payerAcctCode}
        <if test="orderId != null and orderId !=''">
            and a.order_id =#{orderId}
        </if>
        <if test="mobile != null and mobile !=''">
            and a.mobile =#{mobile}
        </if>
        <if test="payResult != null and payResult !=''">
            and a.pay_result =#{payResult}
        </if>
        <if test="startTime != null ">
            and a.apply_time  <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and a.apply_time  <![CDATA[<]]> #{endTime}
        </if>
        <if test="productChannels !=null and productChannels.size() >0">
            and a.product_channel in
            <foreach collection="productChannels" item="productChannels" close=")" separator="," open="(">
                #{productChannels}
            </foreach>
        </if>
        order by a.apply_time desc
    </select>
</mapper>

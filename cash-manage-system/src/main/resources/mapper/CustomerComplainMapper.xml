<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.CustomerComplainMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.CustomerComplain">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="orderNo" column="order_no" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="certNo" column="cert_no" jdbcType="VARCHAR"/>
            <result property="guaranteeCompanyA" column="guarantee_company_a" jdbcType="VARCHAR"/>
            <result property="guaranteeCompanyB" column="guarantee_company_b" jdbcType="VARCHAR"/>
            <result property="rate" column="rate" jdbcType="DECIMAL"/>
            <result property="flowChannel" column="flow_channel" jdbcType="VARCHAR"/>
            <result property="bankChannel" column="bank_channel" jdbcType="VARCHAR"/>
            <result property="complainChannel" column="complain_channel" jdbcType="VARCHAR"/>
            <result property="complainType" column="complain_type" jdbcType="VARCHAR"/>
            <result property="mainItem" column="main_item" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="channelUrl" column="channel_url" jdbcType="VARCHAR"/>
            <result property="overdueState" column="overdue_state" jdbcType="VARCHAR"/>
            <result property="resolveState" column="resolve_state" jdbcType="VARCHAR"/>
            <result property="assistPerson" column="assist_person" jdbcType="VARCHAR"/>
            <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
            <result property="resolveTime" column="resolve_time" jdbcType="TIMESTAMP"/>
            <result property="orderState" column="order_state" jdbcType="VARCHAR"/>
            <result property="subscribeResolveTime" column="subscribe_resolve_time" jdbcType="TIMESTAMP"/>
            <result property="discontinuationState" column="discontinuation_state" jdbcType="VARCHAR"/>
            <result property="handleState" column="handle_state" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="VARCHAR"/>
            <result property="loanAmount" column="loan_amount" jdbcType="DECIMAL"/>
            <result property="completeTime" column="complete_time" jdbcType="TIMESTAMP"/>
            <result property="reduceAmt" column="reduce_amt" jdbcType="DECIMAL"/>
            <result property="revision" column="revision" jdbcType="INTEGER"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_no,name,
        mobile,cert_no,guarantee_company_a,
        guarantee_company_b,rate,flow_channel,bank_channel,
        complain_channel,complain_type,main_item,
        remark,channel_url,overdue_state,
        resolve_state,assist_person,apply_time,
        resolve_time,order_state,subscribe_resolve_time,
        discontinuation_state,handle_state,
        gender,loan_amount,complete_time,reduce_amt,
        revision,created_by,created_time,updated_by,
        updated_time
    </sql>

    <!--条件查询客户投诉-->
    <select id="queryCustomerComplainList" resultType="com.jinghang.cash.pojo.CustomerComplain">
        select
        <include refid="Base_Column_List" />
        from customer_complain where 1=1
        <if test="mobile != null and mobile !=''">
            and mobile =#{mobile}
        </if>
        <if test="orderNo != null and orderNo !=''">
            and order_no =#{orderNo}
        </if>
        <if test="certNo != null and certNo !=''">
            and cert_no =#{certNo}
        </if>
        <if test="bankChannel != null and bankChannel !=''">
            and bank_channel =#{bankChannel}
        </if>
        <if test="complainChannel != null and complainChannel !=''">
            and complain_channel =#{complainChannel}
        </if>
        <if test="complainType != null and complainType !=''">
            and complain_type =#{complainType}
        </if>
        <if test="overdueState != null and overdueState !=''">
            and overdue_state =#{overdueState}
        </if>
        <if test="handleState != null and handleState !=''">
            and handle_state =#{handleState}
        </if>
        <if test="orderState != null and orderState !=''">
            and order_state =#{orderState}
        </if>
        <if test="startTime != null ">
            and created_time  <![CDATA[>=]]> #{startTime}
        </if>
        <if test="endTime != null">
            and created_time  <![CDATA[<=]]> #{endTime}
        </if>
        <if test="completeStartTime != null ">
            and complete_time  <![CDATA[>=]]> #{completeStartTime}
        </if>
        <if test="completeEndTime != null">
            and complete_time  <![CDATA[<=]]> #{completeEndTime}
        </if>
        order by created_time desc
    </select>

</mapper>

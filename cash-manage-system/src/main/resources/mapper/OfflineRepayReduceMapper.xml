<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.OfflineRepayReduceMapper">
    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.OfflineRepayReduce">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
        <result property="period" column="period" jdbcType="INTEGER"/>
        <result property="repayPurpose" column="repay_purpose" jdbcType="VARCHAR"/>
        <result property="principalAmt" column="principal_amt" jdbcType="DECIMAL"/>
        <result property="interestAmt" column="interest_amt" jdbcType="DECIMAL"/>
        <result property="guaranteeAmt" column="guarantee_amt" jdbcType="DECIMAL"/>
        <result property="penaltyAmt" column="penalty_amt" jdbcType="DECIMAL"/>
        <result property="consultFee" column="consult_fee" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="actAmount" column="act_amount" jdbcType="DECIMAL"/>
        <result property="reduceAmount" column="reduce_amount" jdbcType="DECIMAL"/>
        <result property="auditState" column="audit_state" jdbcType="VARCHAR"/>
        <result property="useState" column="use_state" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,remark,revision,
        created_by,created_time,updated_by,
        updated_time,order_id,loan_id,
        period,repay_purpose,principal_amt,
        interest_amt,guarantee_amt,penalty_amt,
        consult_fee,amount,act_amount,
        reduce_amount,audit_state,use_state
    </sql>
    <sql id="search_page_list">
        id
        ,order_id,loan_id,period,repay_purpose,principal_amt,remark,created_by,created_time,updated_by,updated_time,reduce_amount,audit_state,use_state
    </sql>
    <update id="updateBathUseStateByIds">
        update offline_repay_reduce set use_state = 'EXPIRED'
        where id in
        <foreach collection="ids" open="(" separator="," close=")" item="id">
            #{id}
        </foreach>
    </update>
    <select id="search" resultType="com.jinghang.cash.pojo.OfflineRepayReduce">
        select
        <include refid="Base_Column_List"/>
        from offline_repay_reduce
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="orderId != null and orderId !=''">
                and order_id = #{orderId}
            </if>
            <if test="loanId !=null and loanId !=''">
                and loan_id = #{loanId}
            </if>
            <if test="period !=null">
                and period = #{period}
            </if>
            <if test="principalAmt !=null">
                and principal_amt = #{principalAmt}
            </if>
            <if test="interestAmt !=null">
                and interest_amt = #{interestAmt}
            </if>
            <if test="guaranteeAmt !=null">
                and guarantee_amt = #{guaranteeAmt}
            </if>
            <if test="penaltyAmt != null">
                and penalty_amt = #{penaltyAmt}
            </if>
            <if test="consultFee !=null">
                and consult_fee = #{consultFee}
            </if>
            <if test="amount !=null">
                and amount = #{amount}
            </if>
            <if test="actAmount !=null">
                and act_amount = #{actAmount}
            </if>
            <if test="repayPurpose !=null and repayPurpose !=''">
                and repay_purpose = #{repayPurpose}
            </if>
            <if test="reduceAmount !=null">
                and reduce_amount = #{reduceAmount}
            </if>
            <if test="auditState !=null and auditState !=''">
                and audit_state = #{auditState}
            </if>
            <if test="useState !=null and useState !=''">
                and use_state = #{useState}
            </if>
        </where>
    </select>
    <select id="searchPage" resultType="com.jinghang.cash.modules.manage.vo.res.OfflineRepayReduceResponse">
        select
        <include refid="search_page_list"/>
        from offline_repay_reduce
        <where>
            <if test="orderId != null and orderId !=''">
                and order_id = #{orderId}
            </if>
            <if test="auditState !=null and auditState !=''">
                and audit_state = #{auditState}
            </if>
            <if test="useState !=null and useState !=''">
                and use_state = #{useState}
            </if>
            <if test="beginTime !=null and endTime !=null">
                and created_time between #{beginTime} and #{endTime}
            </if>
            <if test="orderIds !=null and orderIds.size() >0">
                and order_id in
                <foreach collection="orderIds" item="orderIdStr" separator="," open="(" close=")">
                    #{orderIdStr}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>
</mapper>

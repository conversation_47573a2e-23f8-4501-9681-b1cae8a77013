<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.OfflineRepayApplyMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.OfflineRepayApply">
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="revision" column="revision" jdbcType="VARCHAR"/>
        <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
        <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
        <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
        <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
        <result property="orderId" column="order_id" jdbcType="VARCHAR"/>
        <result property="loanId" column="loan_id" jdbcType="VARCHAR"/>
        <result property="period" column="period" jdbcType="INTEGER"/>
        <result property="repayPurpose" column="repay_purpose" jdbcType="VARCHAR"/>
        <result property="applyTime" column="apply_time" jdbcType="TIMESTAMP"/>
        <result property="principalAmt" column="principal_amt" jdbcType="DECIMAL"/>
        <result property="interestAmt" column="interest_amt" jdbcType="DECIMAL"/>
        <result property="guaranteeAmt" column="guarantee_amt" jdbcType="DECIMAL"/>
        <result property="penaltyAmt" column="penalty_amt" jdbcType="DECIMAL"/>
        <result property="consultFee" column="consult_fee" jdbcType="DECIMAL"/>
        <result property="amount" column="amount" jdbcType="DECIMAL"/>
        <result property="reduceAmount" column="reduce_amount" jdbcType="DECIMAL"/>
        <result property="actPrincipalAmt" column="act_principal_amt" jdbcType="DECIMAL"/>
        <result property="actInterestAmt" column="act_interest_amt" jdbcType="DECIMAL"/>
        <result property="actGuaranteeAmt" column="act_guarantee_amt" jdbcType="DECIMAL"/>
        <result property="actPenaltyAmt" column="act_penalty_amt" jdbcType="DECIMAL"/>
        <result property="actConsultFee" column="act_consult_fee" jdbcType="DECIMAL"/>
        <result property="actAmount" column="act_amount" jdbcType="DECIMAL"/>
        <result property="overflowAmount" column="overflow_amount" jdbcType="DECIMAL"/>
        <result property="applyState" column="apply_state" jdbcType="DECIMAL"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,remark,revision,
        created_by,created_time,updated_by,
        updated_time,order_id,loan_id,
        period,repay_purpose,apply_time,
        principal_amt,interest_amt,guarantee_amt,
        penalty_amt,consult_fee,amount,
        reduce_amount,act_principal_amt,act_interest_amt,
        act_guarantee_amt,act_penalty_amt,act_consult_fee,
        act_amount,overflow_amount,apply_state
    </sql>
    <sql id="search_page">
        id,order_id,apply_state,repay_purpose,amount,reduce_amount,act_amount,overflow_amount,created_by,created_time,updated_by,updated_time
    </sql>
    <select id="searchPage" resultType="com.jinghang.cash.modules.manage.vo.res.OfflineRepayApplyResponse">
        select
        <include refid="search_page"/>
        from offline_repay_apply
        <where>
            <if test="applyState !=null and applyState !=''">
                and apply_state = #{applyState}
            </if>
            <if test="orderIds !=null and orderIds.size() >0">
                and order_id in
                <foreach collection="orderIds" item="orderIdList" close=")" separator="," open="(">
                    #{orderIdList}
                </foreach>
            </if>
        </where>
        order by id desc
    </select>

    <select id="existsByOrderIdAndPeriodAndApplyState" resultType="boolean" >
        select exists(
            select 1
            from offline_repay_apply
            where order_id = #{orderId}
            and period = #{period}
            and apply_state = #{applyState}
        )
    </select>
</mapper>

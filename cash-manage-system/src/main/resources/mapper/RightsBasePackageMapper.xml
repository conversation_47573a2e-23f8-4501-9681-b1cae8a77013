<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.RightsBasePackageMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.RightsBasePackage">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="outerCode" column="outer_code" jdbcType="VARCHAR"/>
            <result property="packageName" column="package_name" jdbcType="VARCHAR"/>
            <result property="sellingPrice" column="selling_price" jdbcType="DECIMAL"/>
            <result property="costingPrice" column="costing_price" jdbcType="DECIMAL"/>
            <result property="channel" column="channel" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="useStatus" column="use_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,code,outer_code,
        package_name,selling_price,costing_price,
        channel,remark,revision,
        created_by,created_time,updated_by,
        updated_time
    </sql>
    <select id="findByOrderId" resultType="com.jinghang.cash.pojo.RightsBasePackage">
        select r.id,r.code,r.outer_code,
        r.package_name,r.selling_price,r.costing_price,
        r.channel,r.remark,r.revision,
        r.created_by,r.created_time,r.updated_by,
        r.updated_time from rights_base_package r
        join `order` o on r.id = o.rights_package_id
        where o.id = #{orderId}
    </select>
</mapper>

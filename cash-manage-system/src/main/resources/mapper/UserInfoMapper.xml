<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jinghang.cash.mapper.UserInfoMapper">

    <resultMap id="BaseResultMap" type="com.jinghang.cash.pojo.UserInfo">
            <id property="id" column="id" jdbcType="VARCHAR"/>
            <result property="certNo" column="cert_no" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="marriage" column="marriage" jdbcType="VARCHAR"/>
            <result property="education" column="education" jdbcType="VARCHAR"/>
            <result property="acardScore" column="acard_score" jdbcType="VARCHAR"/>
            <result property="bcardScore" column="bcard_score" jdbcType="VARCHAR"/>
            <result property="income" column="income" jdbcType="INTEGER"/>
            <result property="industry" column="industry" jdbcType="VARCHAR"/>
            <result property="position" column="position" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="livingAddress" column="living_address" jdbcType="VARCHAR"/>
            <result property="livingProvinceCode" column="living_province_code" jdbcType="VARCHAR"/>
            <result property="livingCityCode" column="living_city_code" jdbcType="VARCHAR"/>
            <result property="livingDistrictCode" column="living_district_code" jdbcType="VARCHAR"/>
            <result property="livingStreet" column="living_street" jdbcType="VARCHAR"/>
            <result property="unit" column="unit" jdbcType="VARCHAR"/>
            <result property="unitAddress" column="unit_address" jdbcType="VARCHAR"/>
            <result property="unitProvinceCode" column="unit_province_code" jdbcType="VARCHAR"/>
            <result property="unitCityCode" column="unit_city_code" jdbcType="VARCHAR"/>
            <result property="unitDistrictCode" column="unit_district_code" jdbcType="VARCHAR"/>
            <result property="unitStreet" column="unit_street" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="revision" column="revision" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,cert_no,mobile,
        name,marriage,education,
        acard_score,bcard_score,income,
        industry,position,email,
        living_address,living_province_code,living_city_code,
        living_district_code,living_street,unit,
        unit_address,unit_province_code,unit_city_code,
        unit_district_code,unit_street,remark,
        revision,created_by,created_time,
        updated_by,updated_time
    </sql>

    <!--查询用户信息-->
    <select id="queryUserInfo" resultType="com.jinghang.cash.modules.manage.vo.rsp.UserInfoRsp">
        select ui.id as userId, ui.mobile,ui.name,ui.cert_no, coalesce (ur.source_channel,'QHYP') as registerChannel,
        coalesce(ur.created_time, ua.created_time) as registerDate,
        ui.unit, ui.unit_address, ui.unit_phone
        from user_info ui
        left join user_register ur on ui.id = ur.user_id
        left join user_account ua on ui.id = ua.user_id
        WHERE (ur.register_state = 'SUCCEED' or ua.account_state ='1' or ua.account_state ='2' or ua.account_state ='3')
        <if test="mobile != null and mobile !=''">
            and ui.mobile = #{mobile}
        </if>
        <if test="certNo != null and certNo !=''">
            and ui.cert_no = #{certNo}
        </if>
        <if test="userId != null and userId !=''">
            and ui.id = #{userId}
        </if>
        limit 1
    </select>

</mapper>

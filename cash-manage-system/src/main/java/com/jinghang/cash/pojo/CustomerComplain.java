package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.HandleStateEnum;
import com.jinghang.cash.mapper.BaseEntity;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * @TableName customer_complain
 */
@Data
@TableName(value ="customer_complain")
public class CustomerComplain extends BaseEntity {

    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 融担公司A
     */
    private String guaranteeCompanyA;
    /**
     * 融担公司B
     */
    private String guaranteeCompanyB;
    /**
     * 费率
     */
    private BigDecimal rate;
    /**
     * 流量方
     */
    private String flowChannel;
    /**
     * 资金方
     */
    private String bankChannel;
    /**
     * 投诉渠道
     */
    private String complainChannel;
    /**
     * 投诉类型
     */
    private String complainType;
    /**
     * 主要事项
     */
    private String mainItem;
    /**
     * 投诉备注
     */
    private String remark;
    /**
     * 渠道链接
     */
    private String channelUrl;
    /**
     * 是否逾期
     */
    private String overdueState;
    /**
     * 是否解决
     */
    private String resolveState;
    /**
     * 协助人员
     */
    private String assistPerson;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date applyTime;
    /**
     * 处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date resolveTime;
    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 预约处理时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date subscribeResolveTime;
    /**
     * 是否催停
     */
    private String discontinuationState;
    /**
     * 处理状态：待跟进，协商中，申请撤销，已解决
     */
    @Enumerated(EnumType.STRING)
    private HandleStateEnum handleState;
    /**
     * 性别
     */
    private String gender;
    /**
     * 放款金额
     */
    private BigDecimal loanAmount;
    /**
     * 完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date completeTime;
    /**
     * 关怀金/减免金额
     */
    private BigDecimal reduceAmt;

}

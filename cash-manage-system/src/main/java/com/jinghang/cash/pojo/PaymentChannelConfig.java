package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jinghang.cash.enums.WhetherState;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @since 2024-12-27
 */
@TableName(value ="payment_channel_config")
@Data
public class PaymentChannelConfig implements Serializable {

    @TableField(exist = false)
    private static final long serialVersionUID = 5474806085929186275L;

    /**
     * 主键
     */
    @TableId
    private String id;

    /**
     * 支付通道编码
     */
    private String paymentChannelCode;

    /**
     * 支付通道名称
     */
    private String paymentChannelName;

    /**
     * 单笔额度上限
     */
    private BigDecimal singleAmountUpper;

    /**
     * 启用状态
     */
    @Enumerated(EnumType.STRING)
    private WhetherState enabled;

    /**
     * 备注
     */
    private String remark;

    /**
     * 乐观锁
     */
    private int revision;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    private LocalDateTime createdTime;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}

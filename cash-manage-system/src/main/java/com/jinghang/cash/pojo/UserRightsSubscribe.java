package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 用户小卡订阅表
 */
@Data
@TableName("user_rights_subscribe")
public class UserRightsSubscribe implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 2498483630991065397L;

    @TableId
    private String id;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 权益包id
     */
    private String packageId;

    /**
     * 权益包名称
     */
    private String packageName;

    /**
     * 权益供应商
     */
    private String rightsSupplier;

    /**
     * 权益金额
     */
    private BigDecimal rightsAmount;

    /**
     * 权益实际金额
     */
    private BigDecimal rightsActAmount;

    /**
     * 订阅状态 待开通/开通/关闭
     */
    private String subscribeState;

    /**
     * 订阅时间
     */
    private LocalDateTime subscribeTime;

    /**
     * 备注
     */
    private String remark;
    /**
     * 乐观锁
     */
    private Integer revision;
    /**
     * 创建人
     */
    private String createdBy;
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    private LocalDateTime updatedTime;
}

package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.mapper.BaseEntity;
import lombok.Data;

import java.io.Serial;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/5/20
 */
@TableName(value = "aggregate_payment_record")
@Data
public class AggregatePaymentRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = -1008425549126575529L;

    /**
     * 还款模式
     */
    @TableField(value = "repay_purpose")
    private String repayPurpose;
    /**
     * 借据号
     */
    @TableField(value = "loan_id")
    private String loanId;
    /**
     * 订单号
     */
    @TableField(value = "order_id")
    private String orderId;
    /**
     * 用户号
     */
    @TableField(value = "user_id")
    private String userId;
    /**
     * 用户名
     */
    @TableField(value = "name")
    private String name;
    /**
     * 手机号
     */
    @TableField(value = "mobile")
    private String mobile;
    /**
     * 还款期数
     */
    @TableField(value = "period")
    private Integer period;
    /**
     * 发起时间
     * 客服提交订单成功时间
     * 等价于createdTime
     */
    @TableField(value = "apply_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;
    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;
    /**
     * 支付结果：待支付/失败/成功/处理中
     */
    @TableField(value = "pay_result")
    private String payResult;

    /**
     * 订单状态：正常/成功/失败/过期
     */
    @TableField(value = "apply_state")
    private String applyState;

    /**
     * 应还本金
     */
    @TableField(value = "principal_amt")
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    @TableField(value = "interest_amt")
    private BigDecimal interestAmt;
    /**
     * 应还担保费
     */
    @TableField(value = "guarantee_amt")
    private BigDecimal guaranteeAmt;
    /**
     * 应还罚息
     */
    @TableField(value = "penalty_amt")
    private BigDecimal penaltyAmt;
    /**
     * 应还咨询费
     */
    @TableField(value = "consult_fee")
    private BigDecimal consultFee;
    /**
     * 应还总金额
     */
    @TableField(value = "amount")
    private BigDecimal amount;
    /**
     * 减免金额
     */
    @TableField(value = "reduce_amount")
    private BigDecimal reduceAmount;
    /**
     * 实还总金额
     */
    @TableField(value = "act_amount")
    private BigDecimal actAmount;

    /**
     * 支付页面链接
     */
    @TableField(value = "pay_url")
    private String payUrl;

    /**
     * 支付通道
     */
    @TableField(value = "pay_channel")
    private String payChannel;

    /**
     * 产品渠道
     */
    @TableField(value = "product_channel")
    private String productChannel;

}


package com.jinghang.cash.pojo;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * @TableName repay_plan
 */
@TableName(value ="repay_plan")
@Data
public class RepayPlan implements Serializable {
    private String id;

    private String userId;

    private String loanId;

    private Integer period;

    private Date planRepayDate;

    private BigDecimal principalAmt;

    private BigDecimal interestAmt;

    private BigDecimal guaranteeAmt;

    private BigDecimal consultFee;

    private BigDecimal penaltyAmt;

    private BigDecimal amount;

    private String custRepayState;

    private Date actRepayTime;

    private BigDecimal actPrincipalAmt;

    private BigDecimal actInterestAmt;

    private BigDecimal actGuaranteeAmt;

    private BigDecimal actPenaltyAmt;

    private BigDecimal actBreachAmt;

    private BigDecimal actConsultFee;

    private BigDecimal actAmount;

    private String remark;

    private String revision;

    private String createdBy;

    private Date createdTime;

    private String updatedBy;

    private Date updatedTime;

    private static final long serialVersionUID = 1L;
}

package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.modules.manage.Job.dto.OverdueRepayPlanDTO;
import com.jinghang.cash.pojo.RepayPlan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【repay_plan(对客还款计划)】的数据库操作Mapper
* @createDate 2023-11-16 11:44:06
* @Entity com.jinghang.cash.pojo.RepayPlan
*/
@Mapper
@DS("slave")
public interface RepayPlanMapper extends BaseMapper<RepayPlan> {

    RepayPlan queryEarliestOverdue(String loanId);

    RepayPlan findByLoanIdAndPeriod(String loanId, Integer period);

    List<OverdueRepayPlanDTO> statisticOverdueRepay(@Param("date") LocalDate date);
}





package com.jinghang.cash.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.jinghang.cash.pojo.UserRiskRecord;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @description 针对表【user_risk_record(风控)】的数据库操作Mapper
 * @createDate 2023-11-16 20:36:47
 * @Entity com.jinghang.cash.pojo.UserRiskRecord
 */
@Mapper
@DS("slave")
public interface UserRiskRecordMapper extends BaseMapper<UserRiskRecord> {

}





package com.jinghang.cash.modules.manage.vo.rsp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.common.util.DateUtil;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 订单列表
 */
@Data
public class OrderListRsp {

    /**
     * 订单编号
     */
    private String orderId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 期数
     */
    private Integer applyPeriods;
    /**
     * 利率
     */
    private BigDecimal irrRate;
    /**
     * 是否购买权益
     */
    private String rightsMarking;
    /**
     * 审批金额
     */
    private BigDecimal approveAmount;
    /**
     * 放款金额
     */
    private BigDecimal loanAmount;
    /**
     * 权益金额
     */
    private BigDecimal rightsAmount;
    /**
     * 权益实际金额
     */
    private BigDecimal rightsActualAmount;
    /**
     * 权益状态
     */
    private String rightsStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date rightsApplyTime;
    /**
     * 资金方
     */
    private String bankChannel;
    /**
     * 进件渠道
     */
    private String flowChannel;
    /**
     * 订单状态
     */
    private String orderState;
    /**
     * 申请日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyTime;
    /**
     * 放款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date loanTime;

    /**
     * 要款日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date collectTime;

    /**
     * 权益包id
     */
    private String rightsPackageId;

    /**
     * 用户id
     */
    private String userId;

    private BigDecimal sellingPrice;

    /**
     * 是否结清，Y-是  N-否
     */
    private String isClear;
    /**
     * 是否放款，Y-是  N-否
     */
    private String isLoanState;
    /**
     * 结清时间
     */
    private String clearTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = DateUtil.NEW_PATTERN, timezone = "GMT+8")
    private LocalDateTime updatedTime;

}

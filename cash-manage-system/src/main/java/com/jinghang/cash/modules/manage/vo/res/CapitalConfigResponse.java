package com.jinghang.cash.modules.manage.vo.res;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.jinghang.cash.enums.FlowCapitalEnable;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * 资金配置列表
 */
@Data
public class CapitalConfigResponse implements Serializable {

    private static final long serialVersionUID = -2213880755464648869L;

    /**
     * 资金配置id
     */
    private String id;
    /**
     * 资方
     */
    private String bankChannel;
    /**
     * 资方授信日限额
     */
    private BigDecimal creditDayLimit;

    /**
     * 资方放款日限额
     */
    private BigDecimal loanDayLimit;

    /**
     * 支持期数（逗号分割）
     */
    private String periodsRange;
    /**
     * 启用状态
     */
    private FlowCapitalEnable enabled;
    /**
     * 修改人
     */
    private String updatedBy;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedTime;

    /**
     * 资金方
     */
    private String desc;
}

package com.jinghang.cash.modules.manage.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.mapper.CustomerComplainMapper;
import com.jinghang.cash.mapper.OrderMapper;
import com.jinghang.cash.mapper.OrderRemarkMapper;
import com.jinghang.cash.modules.manage.vo.req.CustomerComplainReq;
import com.jinghang.cash.modules.manage.vo.req.OrderRemarkReq;
import com.jinghang.cash.modules.manage.vo.req.UserOrderReq;
import com.jinghang.cash.modules.manage.vo.rsp.ComplaintNoteRsp;
import com.jinghang.cash.pojo.CollPushComplain;
import com.jinghang.cash.pojo.CollPushRemark;
import com.jinghang.cash.pojo.CustomerComplain;
import com.jinghang.cash.pojo.Order;
import com.jinghang.cash.pojo.OrderRemark;
import com.jinghang.cash.service.CustomerComplainService;
import com.jinghang.cash.service.OrderRemarkService;
import com.jinghang.cash.utils.FileUtil;
import com.jinghang.cash.utils.SecurityUtils;

import com.jinghang.common.http.exception.HttpException;
import com.jinghang.common.util.HttpUtil;
import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 客户
 */
@Service
public class CustomerService {

    private static final Logger logger = LoggerFactory.getLogger(CustomerService.class);

    @Autowired
    private CustomerComplainService customerComplainService;

    @Autowired
    private OrderRemarkService orderRemarkService;

    @Autowired
    private CustomerComplainMapper customerComplainMapper;

    @Autowired
    private OrderRemarkMapper orderRemarkMapper;

    @Autowired
    private OrderMapper orderMapper;

    @Value("${cash.manage.url}")
    private String cashManageUrl;

    private final int idLength = 18;

    /**
     * 订单信息中 投诉/备注查询
     *
     * @param userOrderReq
     * @return
     */
    public ComplaintNoteRsp getCustomerComplainNote(UserOrderReq userOrderReq) {
        logger.info("投诉/备注查询参数:{}", JsonUtil.toJsonString(userOrderReq));
        //查询客户头数数据
        QueryWrapper<CustomerComplain> qw = new QueryWrapper<>();
        qw.lambda().eq(CustomerComplain::getOrderNo, userOrderReq.getOrderId()).orderBy(true, false, CustomerComplain::getCreatedTime);
        List<CustomerComplain> customerComplainList = customerComplainService.list(qw);
        ComplaintNoteRsp complaintNoteRsp = new ComplaintNoteRsp();
        complaintNoteRsp.setCustomerComplainList(customerComplainList);
        //查询备注信息
        QueryWrapper<OrderRemark> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderRemark::getOrderNo, userOrderReq.getOrderId()).orderBy(true, false, OrderRemark::getCreatedTime);
        List<OrderRemark> orderRemarkList = orderRemarkService.list(queryWrapper);
        complaintNoteRsp.setOrderRemarkLis(orderRemarkList);
        return complaintNoteRsp;
    }

    /**
     * 分页条件查询投诉数据
     *
     * @param customerComplainReq
     * @return
     */
    public PageInfo<CustomerComplain> queyCustomerComplainList(CustomerComplainReq customerComplainReq) {
        logger.info("分页条件查询投诉参数:{}", JsonUtil.toJsonString(customerComplainReq));
        PageHelper.startPage(customerComplainReq.getPageNum(), customerComplainReq.getPageSize());
        Page<CustomerComplain> lists = customerComplainMapper.queryCustomerComplainList(customerComplainReq);
        PageInfo<CustomerComplain> customerComplainPageInfo = new PageInfo<>(lists);
        logger.info("分页条件查询投诉响应:{}", JsonUtil.toJsonString(customerComplainPageInfo));
        return customerComplainPageInfo;
    }

    /**
     * 订单信息中 新增投诉
     *
     * @param customerComplain
     * @return
     */
    public Boolean addCustomerComplain(CustomerComplain customerComplain) {
        Order order = orderMapper.selectById(customerComplain.getOrderNo());
        if (null != order) {
            customerComplain.setCertNo(order.getCertNo());
            customerComplain.setRate(order.getIrrRate());
            customerComplain.setOrderState(order.getOrderState());
            customerComplain.setBankChannel(order.getBankChannel());
            customerComplain.setFlowChannel(order.getFlowChannel());
        }
        UserDetails currentUser = SecurityUtils.getCurrentUser();
        logger.info("获取当前登录用户信息:{}", JsonUtil.toJsonString(currentUser));
        customerComplain.setCreatedBy(currentUser.getUsername());
        String genId = IdGen.genId(idLength);
        customerComplain.setId(genId);
        logger.info("新增投诉参数:{}", JsonUtil.toJsonString(customerComplain));
        //新增客户投诉数据
        boolean save = customerComplainService.save(customerComplain);
        return sendComplain(customerComplain, save);
    }

    private Boolean sendComplain(CustomerComplain customerComplain, boolean save) {
        if (save) {
            try {
                CollPushComplain collPushComplain = new CollPushComplain();
                collPushComplain.setPushComplaintId(customerComplain.getId());
                HttpUtil.post(cashManageUrl + "/manage/complaint/add", JsonUtil.toJsonString(collPushComplain));
            } catch (HttpException e) {
                logger.info("推送催收失败e:{}", e.getMessage());
            }
        }
        return save;
    }

    private Boolean sendOrderRemark(OrderRemark orderRemark, boolean save) {
        if (save) {
            try {
                CollPushRemark remark = new CollPushRemark();
                remark.setPushRemarkId(orderRemark.getId());
                HttpUtil.post(cashManageUrl + "/manage/remark/add", JsonUtil.toJsonString(remark));
            } catch (HttpException e) {
                logger.info("推送催收失败e:{}", e.getMessage());
            }
        }
        return save;
    }

    /**
     * 订单信息中 编辑投诉
     *
     * @param customerComplain
     * @return
     */
    public Boolean updateCustomerComplain(CustomerComplain customerComplain) {
        logger.info("编辑投诉参数:{}", JsonUtil.toJsonString(customerComplain));
        if (StringUtil.isBlank(customerComplain.getFlowChannel()) || StringUtil.isBlank(customerComplain.getBankChannel())) {
            CustomerComplain record = customerComplainService.getById(customerComplain.getId());
            Order order = orderMapper.selectById(record.getOrderNo());
            customerComplain.setBankChannel(order.getBankChannel());
            customerComplain.setFlowChannel(order.getFlowChannel());
        }
        //编辑客户投诉数据
        boolean update = customerComplainService.updateById(customerComplain);
        return sendComplain(customerComplain, update);
    }

    /**
     * 客户投诉导出
     *
     * @param customerComplainReq
     * @return
     */
    public void exportCustomerComplain(CustomerComplainReq customerComplainReq, HttpServletResponse response) throws IOException {
        logger.info("客户导出投诉请求参数:{}", JsonUtil.toJsonString(customerComplainReq));
        if (StringUtil.isNotBlank(customerComplainReq.getStartTime())) {
            customerComplainReq.setStartTime(customerComplainReq.getStartTime() + " 00:00:00");
            customerComplainReq.setEndTime(customerComplainReq.getEndTime() + " 23:59:59");
        }
        List<CustomerComplain> customerComplains = customerComplainMapper.queryCustomerComplainList(customerComplainReq);
        logger.info("客户导出投诉响应:{}", JsonUtil.toJsonString(customerComplains));
        List<Map<String, Object>> list = new ArrayList<>();
        for (CustomerComplain infoVo : customerComplains) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("订单编号", infoVo.getOrderNo());
            map.put("用户姓名", infoVo.getName());
            map.put("性别", infoVo.getGender());
            map.put("手机号", infoVo.getMobile());
            map.put("身份证号", infoVo.getCertNo());
            map.put("融担公司A", infoVo.getGuaranteeCompanyA());
            map.put("融担公司B", infoVo.getGuaranteeCompanyB());
            map.put("费率", infoVo.getRate());
            map.put("资金方", infoVo.getBankChannel());
            map.put("投诉渠道", infoVo.getComplainChannel());
            map.put("投诉类型", infoVo.getComplainType());
            map.put("主要事项", infoVo.getMainItem());
            map.put("备注", infoVo.getRemark());
            map.put("链接", infoVo.getChannelUrl());
            map.put("贷款金额", infoVo.getLoanAmount());
            map.put("是否逾期", infoVo.getOverdueState());
            map.put("申请时间", infoVo.getApplyTime());
            map.put("处理时间", infoVo.getResolveTime());
            map.put("创建人", infoVo.getCreatedBy());
            map.put("协助人员", infoVo.getAssistPerson());
            map.put("创建时间", infoVo.getCreatedTime());
            map.put("订单状态", infoVo.getOrderState());
            map.put("预约处理时间", infoVo.getSubscribeResolveTime());
            map.put("完成时间", infoVo.getCompleteTime());
            map.put("是否解决", infoVo.getResolveState());
            map.put("关怀金/减免金", infoVo.getReduceAmt());
            map.put("是否催停", infoVo.getDiscontinuationState());
            list.add(map);
        }
        //导出客户投诉数据
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 分页条件查询订单备注
     *
     * @param orderRemarkReq
     * @return
     */
    public PageInfo<OrderRemark> queryOrderRemarkList(OrderRemarkReq orderRemarkReq) {
        if (StringUtil.isNotBlank(orderRemarkReq.getStartTime())) {
            orderRemarkReq.setStartTime(orderRemarkReq.getStartTime() + " 00:00:00");
            orderRemarkReq.setEndTime(orderRemarkReq.getEndTime() + " 23:59:59");
        }
        logger.info("分页条件查询订单备注参数:{}", JsonUtil.toJsonString(orderRemarkReq));
        PageHelper.startPage(orderRemarkReq.getPageNum(), orderRemarkReq.getPageSize());
        Page<OrderRemark> orderRemarks = orderRemarkMapper.queryOrderRemarkList(orderRemarkReq);
        PageInfo<OrderRemark> orderRemarkPageList = new PageInfo<>(orderRemarks);
        logger.info("分页条件查询订单备注响应:{}", JsonUtil.toJsonString(orderRemarkPageList));
        return orderRemarkPageList;
    }

    /**
     * 订单备注导出
     *
     * @param orderRemarkReq
     * @param response
     * @throws IOException
     */
    public void exportOrderRemarkList(OrderRemarkReq orderRemarkReq, HttpServletResponse response) throws IOException {
        logger.info("订单备注导出请求参数:{}", JsonUtil.toJsonString(orderRemarkReq));
        List<OrderRemark> orderRemarks = orderRemarkMapper.queryOrderRemarkList(orderRemarkReq);
        logger.info("订单备注导出投诉响应:{}", JsonUtil.toJsonString(orderRemarks));
        List<Map<String, Object>> list = new ArrayList<>();
        for (OrderRemark infoVo : orderRemarks) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("序号", infoVo.getId()); //序号为添加备注的订单序号
            map.put("订单编号", infoVo.getOrderNo());
            map.put("身份证号", infoVo.getCertNo());
            map.put("用户姓名", infoVo.getName());
            map.put("手机号", infoVo.getMobile());
            map.put("资金方", infoVo.getBankChannel());
            map.put("费率", infoVo.getRate());
            map.put("操作账户", infoVo.getOperateAccount());
            map.put("类型", infoVo.getRemarkType());
            map.put("备注", infoVo.getRemark());
            map.put("创建时间", infoVo.getCreatedTime());
            map.put("更新时间", infoVo.getUpdatedTime());
            list.add(map);
        }
        //导出客户投诉数据
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 订单信息中 新增备注
     *
     * @param orderRemark
     * @return
     */
    public Boolean addOrderRemark(OrderRemark orderRemark) {
        Order order = orderMapper.selectById(orderRemark.getOrderNo());
        if (null != order) {
            orderRemark.setCertNo(order.getCertNo());
            orderRemark.setRate(order.getIrrRate());
            orderRemark.setFlowChannel(order.getFlowChannel());
            orderRemark.setBankChannel(order.getBankChannel());
        }
        String genId = IdGen.genId(idLength);
        orderRemark.setId(genId);
        logger.info("新增备注参数:{}", JsonUtil.toJsonString(orderRemark));
        orderRemark.setOperateAccount(SecurityUtils.getCurrentUsername());
        boolean save = orderRemarkService.save(orderRemark);
        sendOrderRemark(orderRemark, save);
        return save;
    }

    /**
     * 订单信息中 编辑备注
     *
     * @param orderRemark
     * @return
     */
    public Boolean updateOrderRemark(OrderRemark orderRemark) {
        logger.info("编辑备注参数:{}", JsonUtil.toJsonString(orderRemark));
        orderRemark.setOperateAccount(SecurityUtils.getCurrentUsername());
        if (StringUtil.isBlank(orderRemark.getFlowChannel()) || StringUtil.isBlank(orderRemark.getBankChannel())) {
            OrderRemark record = orderRemarkService.getById(orderRemark.getId());
            Order order = orderMapper.selectById(record.getOrderNo());
            orderRemark.setBankChannel(order.getBankChannel());
            orderRemark.setFlowChannel(order.getFlowChannel());
        }
        //新增客户投诉数据
        boolean update = orderRemarkService.updateById(orderRemark);
        sendOrderRemark(orderRemark, update);
        return update;
    }

}

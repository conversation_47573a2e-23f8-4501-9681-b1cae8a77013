package com.jinghang.cash.modules.manage.vo.rsp;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
public class AggregatePaymentRecordResp{

    /**
     * 聚合订单号
     */
    private String id;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 订单状态：正常/成功/失败/过期
     */
    private String applyState;

    /**
     * 发起时间：客服提交订单时间
     */
    private LocalDateTime applyTime;

    /**
     * 支付时间
     */
    private LocalDateTime payTime;

    /**
     * 支付结果：待处理/失败/成功/处理中
     */
    private String payResult;

    /**
     * 还款期数
     */
    private Integer period;

    /**
     * 应还金额
     */
    private BigDecimal amount;

    /**
     * 减免金额
     */
    private BigDecimal reduceAmount;

    /**
     * 减免后金额
     */
    private BigDecimal actAmount;

    /**
     * 支付通道
     */
    private String payChannel;

    /**
     * 项目名称（产品渠道）
     */
    private String productChannel;

    /**
     * 备注
     */
    private String remark;

    /**
     * 支付链接
     */
    private String payUrl;
}

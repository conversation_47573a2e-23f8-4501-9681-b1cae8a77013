package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.enums.VersionType;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;

/**
 * <AUTHOR> gale
 * @Classname QhChannelQueryRequest
 * @Description TODO
 * @Date 2024/3/24 16:32
 */
@Data
public class QhChannelQueryRequest {

    private String channelName;

    private String channelNum;

    @Enumerated(EnumType.STRING)
    private VersionType versionType;

    private String channelNumId;

    private Integer pageNum;
    private Integer pageSize;



}

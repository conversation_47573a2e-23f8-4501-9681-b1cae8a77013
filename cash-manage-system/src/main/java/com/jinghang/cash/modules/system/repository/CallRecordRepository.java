package com.jinghang.cash.modules.system.repository;

import com.jinghang.cash.modules.system.domain.CallRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface CallRecordRepository extends JpaRepository<CallRecord, Long>, JpaSpecificationExecutor<CallRecord> {
    List<CallRecord> findByPhoneOrderByCreateTimeDesc(String phone);
}

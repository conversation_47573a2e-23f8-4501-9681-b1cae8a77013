package com.jinghang.cash.modules.system.domain;

import com.jinghang.cash.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Version;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
@Entity
@Table(name = "call_record")
public class CallRecord extends BaseEntity implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @NotNull(groups = Update.class)
    @ApiModelProperty(value = "ID", hidden = true)
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 主键
     */

    @ApiModelProperty(value = "目标手机号")
    private String phone;

    @ApiModelProperty(value = "订单编号")
    private String orderId;

    @ApiModelProperty(value = "接听情况")
    private String callState;

    @ApiModelProperty(value = "描述")
    private String description;

    @Version
    private Integer revision;

}

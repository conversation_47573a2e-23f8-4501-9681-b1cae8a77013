package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.modules.manage.vo.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RightsRepayPlanReq extends PageParam {
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 身份证号
     */
    private String certNo;

    /**
     * 权益扣费计划
     * @link com.jinghang.cash.enums.RightsStatus
     */
    private String rightsStatus;
}

package com.jinghang.cash.modules.manage.controller;

import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.BankChannel;
import com.jinghang.cash.enums.FlowChannel;
import com.jinghang.cash.modules.manage.RestResult;
import com.jinghang.cash.modules.manage.vo.req.LoanPreFailPageRequest;
import com.jinghang.cash.modules.manage.vo.res.LoanPreFailPageResponse;
import com.jinghang.cash.service.LoanService;
import com.jinghang.common.util.JsonUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/loan")
@Api(tags = "放款接口")
@Slf4j
public class LoanController {

    @Autowired
    private LoanService loanService;

    @GetMapping("flowChannel/query")
    public RestResult<Map<String, String>> queryFlowChannel() {
        return RestResult.success(Arrays.stream(FlowChannel.values()).collect(Collectors.toMap(FlowChannel::name,
            FlowChannel::getDesc, (o1, o2) -> o1, LinkedHashMap::new)));
    }

    @GetMapping("bankChannel/query")
    public RestResult<Map<String, String>> queryBankChannel() {
        return RestResult.success(Arrays.stream(BankChannel.values()).collect(Collectors.toMap(BankChannel::name,
            BankChannel::getName, (o1, o2) -> o1, LinkedHashMap::new)));
    }

    @PostMapping("preFail/query")
    public RestResult<PageInfo<LoanPreFailPageResponse>> queryPreFail(@RequestBody LoanPreFailPageRequest req) {
        PageInfo<LoanPreFailPageResponse> loanPreFailPageResponsePageInfo = loanService.queryPreFail(req);
        return RestResult.success(loanPreFailPageResponsePageInfo);
    }

    @PostMapping("preFail/fail")
    public RestResult<Void> loanFail(@RequestBody List<String> loanIds) {
        loanService.loanFail(loanIds);
        return RestResult.success();
    }

    @PostMapping("/route")
    public RestResult<Void> loanRoute(@RequestBody List<String> loanIds) {
        log.info("放款失败资方路由:{}", JsonUtil.toJsonString(loanIds));

        loanService.loanRoute(loanIds);
        return RestResult.success();
    }

    @PostMapping("/reapply")
    public RestResult<Void> reapply(@RequestBody List<String> loanRecordIds) {
        log.info("原资方重推放款:{}", JsonUtil.toJsonString(loanRecordIds));

        loanService.reapply(loanRecordIds);
        return RestResult.success();
    }
}

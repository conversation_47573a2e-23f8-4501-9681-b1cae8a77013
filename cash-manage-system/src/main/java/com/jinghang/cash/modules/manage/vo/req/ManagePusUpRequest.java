package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.api.enums.AbleStatus;
import com.jinghang.cash.enums.WhetherState;


import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR> gale
 * @Classname ManagePusUpRequest
 * @Description 后管请求
 * @Date 2024/3/22 10:15
 */
public class ManagePusUpRequest {

    /**
     * 客户端编号
     */
    @NotBlank(message = "客户端编号不能为空")
    private String clientNum;

    /**
     * 版本编号
     */
    @NotBlank(message = "客户端编号不能为空")
    private String versionNum;

    /**
     * 版本名称
     */
    @NotBlank(message = "客户端编号不能为空")
    private String versionName;


    /**
     * 升级类型
     * Y强制
     * N非强制
     */
    @NotNull(message = "客户端编号不能为空")
    @Enumerated(EnumType.STRING)
    private WhetherState pushType;

    /**
     * 开启-DISABLE
     * 关闭-ENABLE
     */
    @NotNull(message = "客户端编号不能为空")
    @Enumerated(EnumType.STRING)
    private AbleStatus pushEnable;

    /**
     * 推送内容
     */
    @NotBlank(message = "客户端编号不能为空")
    private String pushContext;



    /**
     * 推送链接
     */
    private String pushUrl;

    /**
     * 渠道列表
     */
    private List<String> channelList;


    public String getClientNum() {
        return clientNum;
    }

    public void setClientNum(String clientNum) {
        this.clientNum = clientNum;
    }

    public String getVersionNum() {
        return versionNum;
    }

    public void setVersionNum(String versionNum) {
        this.versionNum = versionNum;
    }

    public String getVersionName() {
        return versionName;
    }

    public void setVersionName(String versionName) {
        this.versionName = versionName;
    }

    public WhetherState getPushType() {
        return pushType;
    }

    public void setPushType(WhetherState pushType) {
        this.pushType = pushType;
    }

    public AbleStatus getPushEnable() {
        return pushEnable;
    }

    public void setPushEnable(AbleStatus pushEnable) {
        this.pushEnable = pushEnable;
    }

    public String getPushContext() {
        return pushContext;
    }

    public void setPushContext(String pushContext) {
        this.pushContext = pushContext;
    }

    public String getPushUrl() {
        return pushUrl;
    }

    public void setPushUrl(String pushUrl) {
        this.pushUrl = pushUrl;
    }

    public List<String> getChannelList() {
        return channelList;
    }

    public void setChannelList(List<String> channelList) {
        this.channelList = channelList;
    }
}

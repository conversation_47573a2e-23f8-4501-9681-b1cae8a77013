package com.jinghang.cash.modules.manage.vo.req;

import com.jinghang.cash.modules.manage.vo.PageParam;

import java.math.BigDecimal;

public class QueryCouponReq extends PageParam {
    /**
     * 主键
     */
    private String id;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 优惠券类型
     */
    private String couponType;
    /**
     * 优惠券有效期
     */
    private Integer validDays;
    /**
     * 发放方式
     */
    private Integer sendType;
    /**
     * 适用期数
     */
    private String applicableTerms;
    /**
     * 额度
     */
    private BigDecimal amount;
    /**
     * 折扣
     */
    private String discount;
    /**
     * 最低金额
     */
    private BigDecimal minAmount;
    /**
     * 最高金额
     */
    private BigDecimal maxAmount;
    /**
     * 是否支持弹窗（1是，0否）
     */
    private Integer isPopup;
    /**
     * 弹窗图片
     */
    private String pictureUrl;

    /**
     * 优惠券备注
     * @return
     */
    private String couponRemark;

    /**
     * 更新人
     */
    private String updatedBy;
    /**
     * 适用系统
     */
    private String applicableSystems;
    /**
     * 生效状态（1生效，0失效）
     */
    private String isActive;
}

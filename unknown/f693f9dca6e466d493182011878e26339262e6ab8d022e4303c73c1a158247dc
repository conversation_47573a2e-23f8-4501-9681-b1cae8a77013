package com.jinghang.capital.core.banks.cybk.dto;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 请求、响应  公共参数
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCommonData {
    private String channel;
    private String key;
    private String json;
    private String sign;
    private String randomStr;

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getJson() {
        return json;
    }

    public void setJson(String json) {
        this.json = json;
    }

    public String getSign() {
        return sign;
    }

    public void setSign(String sign) {
        this.sign = sign;
    }

    public String getRandomStr() {
        return randomStr;
    }

    public void setRandomStr(String randomStr) {
        this.randomStr = randomStr;
    }
}
